using System;
using System.Collections;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;

namespace TraceLens.ThemeControl.Controls
{
    /// <summary>
    /// HTable - 仿照 Element UI 的表格控件
    /// 支持排序、选择、分页、斑马纹等功能
    /// </summary>
    public class HTable : Control
    {
        #region 依赖属性

        /// <summary>
        /// 数据源
        /// </summary>
        public static readonly DependencyProperty ItemsSourceProperty =
            DependencyProperty.Register(nameof(ItemsSource), typeof(IEnumerable), typeof(HTable),
                new PropertyMetadata(null, OnItemsSourceChanged));

        /// <summary>
        /// 列定义集合
        /// </summary>
        public static readonly DependencyProperty ColumnsProperty =
            DependencyProperty.Register(nameof(Columns), typeof(ObservableCollection<HTableColumn>), typeof(HTable),
                new PropertyMetadata(null));

        /// <summary>
        /// 选中的项
        /// </summary>
        public static readonly DependencyProperty SelectedItemProperty =
            DependencyProperty.Register(nameof(SelectedItem), typeof(object), typeof(HTable),
                new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSelectedItemChanged));

        /// <summary>
        /// 选中的项集合
        /// </summary>
        public static readonly DependencyProperty SelectedItemsProperty =
            DependencyProperty.Register(nameof(SelectedItems), typeof(IList), typeof(HTable),
                new PropertyMetadata(null));

        /// <summary>
        /// 是否显示边框
        /// </summary>
        public static readonly DependencyProperty ShowBorderProperty =
            DependencyProperty.Register(nameof(ShowBorder), typeof(bool), typeof(HTable),
                new PropertyMetadata(true));

        /// <summary>
        /// 边框样式
        /// </summary>
        public static readonly DependencyProperty BorderStyleProperty =
            DependencyProperty.Register(nameof(BorderStyle), typeof(HTableBorderStyle), typeof(HTable),
                new PropertyMetadata(HTableBorderStyle.All));

        /// <summary>
        /// 是否显示外边框
        /// </summary>
        public static readonly DependencyProperty ShowOuterBorderProperty =
            DependencyProperty.Register(nameof(ShowOuterBorder), typeof(bool), typeof(HTable),
                new PropertyMetadata(true));

        /// <summary>
        /// 是否显示内边框
        /// </summary>
        public static readonly DependencyProperty ShowInnerBorderProperty =
            DependencyProperty.Register(nameof(ShowInnerBorder), typeof(bool), typeof(HTable),
                new PropertyMetadata(true));

        /// <summary>
        /// 是否显示斑马纹
        /// </summary>
        public static readonly DependencyProperty ShowStripeProperty =
            DependencyProperty.Register(nameof(ShowStripe), typeof(bool), typeof(HTable),
                new PropertyMetadata(false));

        /// <summary>
        /// 是否显示表头
        /// </summary>
        public static readonly DependencyProperty ShowHeaderProperty =
            DependencyProperty.Register(nameof(ShowHeader), typeof(bool), typeof(HTable),
                new PropertyMetadata(true));

        /// <summary>
        /// 表格大小
        /// </summary>
        public static readonly DependencyProperty TableSizeProperty =
            DependencyProperty.Register(nameof(TableSize), typeof(HTableSize), typeof(HTable),
                new PropertyMetadata(HTableSize.Default));

        /// <summary>
        /// 行高
        /// </summary>
        public static readonly DependencyProperty RowHeightProperty =
            DependencyProperty.Register(nameof(RowHeight), typeof(double), typeof(HTable),
                new PropertyMetadata(48.0));

        /// <summary>
        /// 表头高度
        /// </summary>
        public static readonly DependencyProperty HeaderHeightProperty =
            DependencyProperty.Register(nameof(HeaderHeight), typeof(double), typeof(HTable),
                new PropertyMetadata(48.0));

        /// <summary>
        /// 是否可多选
        /// </summary>
        public static readonly DependencyProperty IsMultiSelectProperty =
            DependencyProperty.Register(nameof(IsMultiSelect), typeof(bool), typeof(HTable),
                new PropertyMetadata(false));

        /// <summary>
        /// 空数据文本
        /// </summary>
        public static readonly DependencyProperty EmptyTextProperty =
            DependencyProperty.Register(nameof(EmptyText), typeof(string), typeof(HTable),
                new PropertyMetadata("暂无数据"));

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public static readonly DependencyProperty IsLoadingProperty =
            DependencyProperty.Register(nameof(IsLoading), typeof(bool), typeof(HTable),
                new PropertyMetadata(false));

        /// <summary>
        /// 加载文本
        /// </summary>
        public static readonly DependencyProperty LoadingTextProperty =
            DependencyProperty.Register(nameof(LoadingText), typeof(string), typeof(HTable),
                new PropertyMetadata("加载中..."));

        /// <summary>
        /// 标签文本
        /// </summary>
        public static readonly DependencyProperty LabelProperty =
            DependencyProperty.Register(nameof(Label), typeof(string), typeof(HTable),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 标签宽度
        /// </summary>
        public static readonly DependencyProperty LabelWidthProperty =
            DependencyProperty.Register(nameof(LabelWidth), typeof(double), typeof(HTable),
                new PropertyMetadata(80.0));

        /// <summary>
        /// 标签位置
        /// </summary>
        public static readonly DependencyProperty LabelPositionProperty =
            DependencyProperty.Register(nameof(LabelPosition), typeof(HLabelPosition), typeof(HTable),
                new PropertyMetadata(HLabelPosition.Left));

        /// <summary>
        /// 是否显示冒号
        /// </summary>
        public static readonly DependencyProperty ShowColonProperty =
            DependencyProperty.Register(nameof(ShowColon), typeof(bool), typeof(HTable),
                new PropertyMetadata(true));

        /// <summary>
        /// 是否显示合计栏
        /// </summary>
        public static readonly DependencyProperty ShowSummaryProperty =
            DependencyProperty.Register(nameof(ShowSummary), typeof(bool), typeof(HTable),
                new PropertyMetadata(false, OnShowSummaryChanged));

        /// <summary>
        /// 合计数据
        /// </summary>
        public static readonly DependencyProperty SummaryDataProperty =
            DependencyProperty.Register(nameof(SummaryData), typeof(object), typeof(HTable),
                new PropertyMetadata(null, OnSummaryDataChanged));

        /// <summary>
        /// 合计栏标题
        /// </summary>
        public static readonly DependencyProperty SummaryTitleProperty =
            DependencyProperty.Register(nameof(SummaryTitle), typeof(string), typeof(HTable),
                new PropertyMetadata("合计"));

        /// <summary>
        /// 合计栏高度
        /// </summary>
        public static readonly DependencyProperty SummaryHeightProperty =
            DependencyProperty.Register(nameof(SummaryHeight), typeof(double), typeof(HTable),
                new PropertyMetadata(48.0));

        #endregion

        #region 属性

        /// <summary>
        /// 数据源
        /// </summary>
        public IEnumerable ItemsSource
        {
            get => (IEnumerable)GetValue(ItemsSourceProperty);
            set => SetValue(ItemsSourceProperty, value);
        }

        /// <summary>
        /// 列定义集合
        /// </summary>
        public ObservableCollection<HTableColumn> Columns
        {
            get => (ObservableCollection<HTableColumn>)GetValue(ColumnsProperty);
            set => SetValue(ColumnsProperty, value);
        }

        /// <summary>
        /// 选中的项
        /// </summary>
        public object SelectedItem
        {
            get => GetValue(SelectedItemProperty);
            set => SetValue(SelectedItemProperty, value);
        }

        /// <summary>
        /// 选中的项集合
        /// </summary>
        public IList SelectedItems
        {
            get => (IList)GetValue(SelectedItemsProperty);
            set => SetValue(SelectedItemsProperty, value);
        }

        /// <summary>
        /// 是否显示边框
        /// </summary>
        public bool ShowBorder
        {
            get => (bool)GetValue(ShowBorderProperty);
            set => SetValue(ShowBorderProperty, value);
        }

        /// <summary>
        /// 边框样式
        /// </summary>
        public HTableBorderStyle BorderStyle
        {
            get => (HTableBorderStyle)GetValue(BorderStyleProperty);
            set => SetValue(BorderStyleProperty, value);
        }

        /// <summary>
        /// 是否显示外边框
        /// </summary>
        public bool ShowOuterBorder
        {
            get => (bool)GetValue(ShowOuterBorderProperty);
            set => SetValue(ShowOuterBorderProperty, value);
        }

        /// <summary>
        /// 是否显示内边框
        /// </summary>
        public bool ShowInnerBorder
        {
            get => (bool)GetValue(ShowInnerBorderProperty);
            set => SetValue(ShowInnerBorderProperty, value);
        }

        /// <summary>
        /// 是否显示斑马纹
        /// </summary>
        public bool ShowStripe
        {
            get => (bool)GetValue(ShowStripeProperty);
            set => SetValue(ShowStripeProperty, value);
        }

        /// <summary>
        /// 是否显示表头
        /// </summary>
        public bool ShowHeader
        {
            get => (bool)GetValue(ShowHeaderProperty);
            set => SetValue(ShowHeaderProperty, value);
        }

        /// <summary>
        /// 表格大小
        /// </summary>
        public HTableSize TableSize
        {
            get => (HTableSize)GetValue(TableSizeProperty);
            set => SetValue(TableSizeProperty, value);
        }

        /// <summary>
        /// 行高
        /// </summary>
        public double RowHeight
        {
            get => (double)GetValue(RowHeightProperty);
            set => SetValue(RowHeightProperty, value);
        }

        /// <summary>
        /// 表头高度
        /// </summary>
        public double HeaderHeight
        {
            get => (double)GetValue(HeaderHeightProperty);
            set => SetValue(HeaderHeightProperty, value);
        }

        /// <summary>
        /// 是否可多选
        /// </summary>
        public bool IsMultiSelect
        {
            get => (bool)GetValue(IsMultiSelectProperty);
            set => SetValue(IsMultiSelectProperty, value);
        }

        /// <summary>
        /// 空数据文本
        /// </summary>
        public string EmptyText
        {
            get => (string)GetValue(EmptyTextProperty);
            set => SetValue(EmptyTextProperty, value);
        }

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get => (bool)GetValue(IsLoadingProperty);
            set => SetValue(IsLoadingProperty, value);
        }

        /// <summary>
        /// 加载文本
        /// </summary>
        public string LoadingText
        {
            get => (string)GetValue(LoadingTextProperty);
            set => SetValue(LoadingTextProperty, value);
        }

        /// <summary>
        /// 标签文本
        /// </summary>
        public string Label
        {
            get => (string)GetValue(LabelProperty);
            set => SetValue(LabelProperty, value);
        }

        /// <summary>
        /// 标签宽度
        /// </summary>
        public double LabelWidth
        {
            get => (double)GetValue(LabelWidthProperty);
            set => SetValue(LabelWidthProperty, value);
        }

        /// <summary>
        /// 标签位置
        /// </summary>
        public HLabelPosition LabelPosition
        {
            get => (HLabelPosition)GetValue(LabelPositionProperty);
            set => SetValue(LabelPositionProperty, value);
        }

        /// <summary>
        /// 是否显示冒号
        /// </summary>
        public bool ShowColon
        {
            get => (bool)GetValue(ShowColonProperty);
            set => SetValue(ShowColonProperty, value);
        }

        /// <summary>
        /// 是否显示合计栏
        /// </summary>
        public bool ShowSummary
        {
            get => (bool)GetValue(ShowSummaryProperty);
            set => SetValue(ShowSummaryProperty, value);
        }

        /// <summary>
        /// 合计数据
        /// </summary>
        public object SummaryData
        {
            get => GetValue(SummaryDataProperty);
            set => SetValue(SummaryDataProperty, value);
        }

        /// <summary>
        /// 合计栏标题
        /// </summary>
        public string SummaryTitle
        {
            get => (string)GetValue(SummaryTitleProperty);
            set => SetValue(SummaryTitleProperty, value);
        }

        /// <summary>
        /// 合计栏高度
        /// </summary>
        public double SummaryHeight
        {
            get => (double)GetValue(SummaryHeightProperty);
            set => SetValue(SummaryHeightProperty, value);
        }

        #endregion

        #region 路由事件

        /// <summary>
        /// 选择改变事件
        /// </summary>
        public static readonly RoutedEvent SelectionChangedEvent =
            EventManager.RegisterRoutedEvent(nameof(SelectionChanged), RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(HTable));

        /// <summary>
        /// 行点击事件
        /// </summary>
        public static readonly RoutedEvent RowClickEvent =
            EventManager.RegisterRoutedEvent(nameof(RowClick), RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(HTable));

        /// <summary>
        /// 行双击事件
        /// </summary>
        public static readonly RoutedEvent RowDoubleClickEvent =
            EventManager.RegisterRoutedEvent(nameof(RowDoubleClick), RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(HTable));

        /// <summary>
        /// 排序改变事件
        /// </summary>
        public static readonly RoutedEvent SortChangedEvent =
            EventManager.RegisterRoutedEvent(nameof(SortChanged), RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(HTable));

        /// <summary>
        /// 选择改变事件
        /// </summary>
        public event RoutedEventHandler SelectionChanged
        {
            add => AddHandler(SelectionChangedEvent, value);
            remove => RemoveHandler(SelectionChangedEvent, value);
        }

        /// <summary>
        /// 行点击事件
        /// </summary>
        public event RoutedEventHandler RowClick
        {
            add => AddHandler(RowClickEvent, value);
            remove => RemoveHandler(RowClickEvent, value);
        }

        /// <summary>
        /// 行双击事件
        /// </summary>
        public event RoutedEventHandler RowDoubleClick
        {
            add => AddHandler(RowDoubleClickEvent, value);
            remove => RemoveHandler(RowDoubleClickEvent, value);
        }

        /// <summary>
        /// 排序改变事件
        /// </summary>
        public event RoutedEventHandler SortChanged
        {
            add => AddHandler(SortChangedEvent, value);
            remove => RemoveHandler(SortChangedEvent, value);
        }

        #endregion

        #region 私有字段

        private DataGrid? _dataGrid = null;
        private TextBlock? _emptyTextBlock = null;
        private TextBlock? _loadingTextBlock = null;
        private Grid? _loadingOverlay = null;
        private ItemsControl? _summaryItemsControl = null;
        private ScrollViewer? _summaryScrollViewer = null;
        private ScrollViewer? _mainScrollViewer = null;

        #endregion

        #region 构造函数

        static HTable()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(HTable), new FrameworkPropertyMetadata(typeof(HTable)));
        }

        public HTable()
        {
            // 设置默认值
            ItemsSource = null;
            Columns = new ObservableCollection<HTableColumn>();
            SelectedItem = null;
            SelectedItems = new ObservableCollection<object>();
            ShowBorder = true;
            BorderStyle = HTableBorderStyle.All;
            ShowOuterBorder = true;
            ShowInnerBorder = true;
            ShowStripe = false;
            ShowHeader = true;
            TableSize = HTableSize.Default;
            RowHeight = 48.0;
            HeaderHeight = 48.0;
            IsMultiSelect = false;
            EmptyText = "暂无数据";
            IsLoading = false;
            LoadingText = "加载中...";
            Label = string.Empty;
            LabelWidth = 80.0;
            LabelPosition = HLabelPosition.Left;
            ShowColon = true;
            ShowSummary = false;
            SummaryData = null;
            SummaryTitle = "合计";
            SummaryHeight = 48.0;

            // 设置控件默认属性
            IsTabStop = true;
            Focusable = true;

            Loaded += OnLoaded;
        }

        #endregion

        #region 重写方法

        public override void OnApplyTemplate()
        {
            base.OnApplyTemplate();

            // 解绑旧事件
            UnbindEvents();

            // 获取模板中的控件
            _dataGrid = GetTemplateChild("PART_DataGrid") as DataGrid;
            _emptyTextBlock = GetTemplateChild("PART_EmptyText") as TextBlock;
            _loadingTextBlock = GetTemplateChild("PART_LoadingText") as TextBlock;
            _loadingOverlay = GetTemplateChild("PART_LoadingOverlay") as Grid;
            _summaryItemsControl = GetTemplateChild("PART_SummaryItemsControl") as ItemsControl;
            _summaryScrollViewer = GetTemplateChild("PART_SummaryScrollViewer") as ScrollViewer;
            _mainScrollViewer = GetTemplateChild("DG_ScrollViewer") as ScrollViewer;

            // 绑定事件
            BindEvents();

            UpdateVisualState();
            UpdateDataGrid();
            UpdateSummaryData();
        }

        private void BindEvents()
        {
            if (_dataGrid != null)
            {
                _dataGrid.SelectionChanged += OnDataGridSelectionChanged;
                _dataGrid.MouseDoubleClick += OnDataGridMouseDoubleClick;
                _dataGrid.Sorting += OnDataGridSorting;
                _dataGrid.ColumnDisplayIndexChanged += OnDataGridColumnDisplayIndexChanged;
            }

            if (_mainScrollViewer != null)
            {
                _mainScrollViewer.ScrollChanged += OnMainScrollViewerScrollChanged;
            }
        }

        private void UnbindEvents()
        {
            if (_dataGrid != null)
            {
                _dataGrid.SelectionChanged -= OnDataGridSelectionChanged;
                _dataGrid.MouseDoubleClick -= OnDataGridMouseDoubleClick;
                _dataGrid.Sorting -= OnDataGridSorting;
            }
        }

        #endregion

        #region 私有方法

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            UpdateVisualState();
        }

        private static void OnItemsSourceChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HTable table)
            {
                table.OnItemsSourceChanged();
            }
        }

        private static void OnSelectedItemChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HTable table)
            {
                table.OnSelectedItemChanged();
            }
        }

        private void OnItemsSourceChanged()
        {
            UpdateDataGrid();
            UpdateVisualState();
        }

        private void OnSelectedItemChanged()
        {
            if (_dataGrid != null)
            {
                _dataGrid.SelectedItem = SelectedItem;
            }
            RaiseEvent(new RoutedEventArgs(SelectionChangedEvent));
        }

        private void UpdateDataGrid()
        {
            if (_dataGrid == null) return;

            _dataGrid.ItemsSource = ItemsSource;
            _dataGrid.SelectedItem = SelectedItem;

            // 更新列定义
            _dataGrid.Columns.Clear();
            if (Columns != null)
            {
                foreach (var column in Columns)
                {
                    var dataGridColumn = CreateDataGridColumn(column);
                    _dataGrid.Columns.Add(dataGridColumn);
                }
            }
        }

        private DataGridColumn CreateDataGridColumn(HTableColumn column)
        {
            DataGridColumn dataGridColumn;

            switch (column.ColumnType)
            {
                case HTableColumnType.Text:
                    dataGridColumn = new DataGridTextColumn
                    {
                        Binding = new Binding(column.Property)
                    };
                    break;
                case HTableColumnType.CheckBox:
                    dataGridColumn = new DataGridCheckBoxColumn
                    {
                        Binding = new Binding(column.Property)
                    };
                    break;
                case HTableColumnType.Template:
                    dataGridColumn = new DataGridTemplateColumn
                    {
                        CellTemplate = column.CellTemplate
                    };
                    break;
                default:
                    dataGridColumn = new DataGridTextColumn
                    {
                        Binding = new Binding(column.Property)
                    };
                    break;
            }

            dataGridColumn.Header = column.Header;
            dataGridColumn.Width = column.Width;
            dataGridColumn.CanUserSort = column.Sortable;
            dataGridColumn.CanUserResize = column.Resizable;

            return dataGridColumn;
        }

        private void UpdateVisualState()
        {
            // 更新空数据显示
            if (_emptyTextBlock != null)
            {
                var hasData = ItemsSource != null && ItemsSource.Cast<object>().Any();
                _emptyTextBlock.Visibility = !hasData && !IsLoading ? Visibility.Visible : Visibility.Collapsed;
            }

            // 更新加载状态
            if (_loadingOverlay != null)
            {
                _loadingOverlay.Visibility = IsLoading ? Visibility.Visible : Visibility.Collapsed;
            }

            // 更新表格样式
            if (_dataGrid != null)
            {
                _dataGrid.HeadersVisibility = ShowHeader ? DataGridHeadersVisibility.Column : DataGridHeadersVisibility.None;

                // 根据边框设置更新网格线显示
                if (ShowBorder)
                {
                    switch (BorderStyle)
                    {
                        case HTableBorderStyle.All:
                            _dataGrid.GridLinesVisibility = DataGridGridLinesVisibility.All;
                            break;
                        case HTableBorderStyle.Horizontal:
                            _dataGrid.GridLinesVisibility = DataGridGridLinesVisibility.Horizontal;
                            break;
                        case HTableBorderStyle.Vertical:
                            _dataGrid.GridLinesVisibility = DataGridGridLinesVisibility.Vertical;
                            break;
                        case HTableBorderStyle.None:
                            _dataGrid.GridLinesVisibility = DataGridGridLinesVisibility.None;
                            break;
                    }
                }
                else
                {
                    _dataGrid.GridLinesVisibility = DataGridGridLinesVisibility.None;
                }

                _dataGrid.AlternatingRowBackground = ShowStripe ? new SolidColorBrush(Color.FromArgb(25, 64, 158, 255)) : null;
                _dataGrid.RowHeight = RowHeight;
                _dataGrid.ColumnHeaderHeight = HeaderHeight;
                _dataGrid.SelectionMode = IsMultiSelect ? DataGridSelectionMode.Extended : DataGridSelectionMode.Single;
            }
        }

        private void OnDataGridSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_dataGrid != null)
            {
                SelectedItem = _dataGrid.SelectedItem;
                if (SelectedItems is ObservableCollection<object> selectedItems)
                {
                    selectedItems.Clear();
                    foreach (var item in _dataGrid.SelectedItems)
                    {
                        selectedItems.Add(item);
                    }
                }
            }
            RaiseEvent(new RoutedEventArgs(SelectionChangedEvent));
        }

        private void OnDataGridMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            RaiseEvent(new RoutedEventArgs(RowDoubleClickEvent));
        }

        private void OnDataGridSorting(object sender, DataGridSortingEventArgs e)
        {
            RaiseEvent(new RoutedEventArgs(SortChangedEvent));
        }

        private void OnDataGridColumnDisplayIndexChanged(object sender, DataGridColumnEventArgs e)
        {
            UpdateSummaryData();
        }

        private void OnMainScrollViewerScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            // 同步合计栏的水平滚动
            if (_summaryScrollViewer != null)
            {
                _summaryScrollViewer.ScrollToHorizontalOffset(e.HorizontalOffset);
            }
        }

        private static void OnShowSummaryChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HTable table)
            {
                table.UpdateSummaryVisibility();
            }
        }

        private static void OnSummaryDataChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HTable table)
            {
                table.UpdateSummaryData();
            }
        }

        private void UpdateSummaryVisibility()
        {
            // 这个方法会在 XAML 模板中通过触发器处理
        }

        private void UpdateSummaryData()
        {
            if (!ShowSummary || _summaryItemsControl == null || _dataGrid == null) return;

            var summaryItems = new List<HTableSummaryItem>();

            for (int i = 0; i < _dataGrid.Columns.Count; i++)
            {
                var column = _dataGrid.Columns[i];
                if (column.Visibility == Visibility.Visible)
                {
                    var content = GetSummaryContent(i, column);
                    summaryItems.Add(new HTableSummaryItem
                    {
                        Width = column.ActualWidth,
                        Content = content,
                        Index = column.DisplayIndex
                    });
                }
            }

            // 按显示顺序排序
            summaryItems = summaryItems.OrderBy(x => x.Index).ToList();
            _summaryItemsControl.ItemsSource = summaryItems;
        }

        private string GetSummaryContent(int columnIndex, DataGridColumn column)
        {
            if (SummaryData == null) return "-";

            // 如果是第一列，显示合计标题
            if (columnIndex == 0)
            {
                return SummaryTitle;
            }

            // 如果 SummaryData 是字符串数组
            if (SummaryData is IList<string> stringList)
            {
                return columnIndex < stringList.Count ? stringList[columnIndex] : "-";
            }

            // 如果 SummaryData 是对象，通过反射获取属性值
            if (SummaryData is object obj && !string.IsNullOrEmpty(column.SortMemberPath))
            {
                var property = obj.GetType().GetProperty(column.SortMemberPath);
                if (property != null)
                {
                    var value = property.GetValue(obj, null);
                    return value?.ToString() ?? "-";
                }
            }

            return "-";
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 刷新表格数据
        /// </summary>
        public void Refresh()
        {
            UpdateDataGrid();
            UpdateVisualState();
        }

        /// <summary>
        /// 清除选择
        /// </summary>
        public void ClearSelection()
        {
            if (_dataGrid != null)
            {
                _dataGrid.SelectedItem = null;
                _dataGrid.SelectedItems.Clear();
            }
            SelectedItem = null;
            if (SelectedItems is ObservableCollection<object> selectedItems)
            {
                selectedItems.Clear();
            }
        }

        /// <summary>
        /// 选择所有项
        /// </summary>
        public void SelectAll()
        {
            if (_dataGrid != null && IsMultiSelect)
            {
                _dataGrid.SelectAll();
            }
        }

        #endregion
    }

    /// <summary>
    /// 表格列定义
    /// </summary>
    public class HTableColumn : DependencyObject
    {
        /// <summary>
        /// 列标题
        /// </summary>
        public static readonly DependencyProperty HeaderProperty =
            DependencyProperty.Register(nameof(Header), typeof(string), typeof(HTableColumn),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 绑定属性
        /// </summary>
        public static readonly DependencyProperty PropertyProperty =
            DependencyProperty.Register(nameof(Property), typeof(string), typeof(HTableColumn),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 列宽度
        /// </summary>
        public static readonly DependencyProperty WidthProperty =
            DependencyProperty.Register(nameof(Width), typeof(DataGridLength), typeof(HTableColumn),
                new PropertyMetadata(DataGridLength.Auto));

        /// <summary>
        /// 是否可排序
        /// </summary>
        public static readonly DependencyProperty SortableProperty =
            DependencyProperty.Register(nameof(Sortable), typeof(bool), typeof(HTableColumn),
                new PropertyMetadata(true));

        /// <summary>
        /// 是否可调整大小
        /// </summary>
        public static readonly DependencyProperty ResizableProperty =
            DependencyProperty.Register(nameof(Resizable), typeof(bool), typeof(HTableColumn),
                new PropertyMetadata(true));

        /// <summary>
        /// 列类型
        /// </summary>
        public static readonly DependencyProperty ColumnTypeProperty =
            DependencyProperty.Register(nameof(ColumnType), typeof(HTableColumnType), typeof(HTableColumn),
                new PropertyMetadata(HTableColumnType.Text));

        /// <summary>
        /// 单元格模板
        /// </summary>
        public static readonly DependencyProperty CellTemplateProperty =
            DependencyProperty.Register(nameof(CellTemplate), typeof(DataTemplate), typeof(HTableColumn),
                new PropertyMetadata(null));

        /// <summary>
        /// 列标题
        /// </summary>
        public string Header
        {
            get => (string)GetValue(HeaderProperty);
            set => SetValue(HeaderProperty, value);
        }

        /// <summary>
        /// 绑定属性
        /// </summary>
        public string Property
        {
            get => (string)GetValue(PropertyProperty);
            set => SetValue(PropertyProperty, value);
        }

        /// <summary>
        /// 列宽度
        /// </summary>
        public DataGridLength Width
        {
            get => (DataGridLength)GetValue(WidthProperty);
            set => SetValue(WidthProperty, value);
        }

        /// <summary>
        /// 是否可排序
        /// </summary>
        public bool Sortable
        {
            get => (bool)GetValue(SortableProperty);
            set => SetValue(SortableProperty, value);
        }

        /// <summary>
        /// 是否可调整大小
        /// </summary>
        public bool Resizable
        {
            get => (bool)GetValue(ResizableProperty);
            set => SetValue(ResizableProperty, value);
        }

        /// <summary>
        /// 列类型
        /// </summary>
        public HTableColumnType ColumnType
        {
            get => (HTableColumnType)GetValue(ColumnTypeProperty);
            set => SetValue(ColumnTypeProperty, value);
        }

        /// <summary>
        /// 单元格模板
        /// </summary>
        public DataTemplate CellTemplate
        {
            get => (DataTemplate)GetValue(CellTemplateProperty);
            set => SetValue(CellTemplateProperty, value);
        }
    }

    /// <summary>
    /// 表格大小枚举
    /// </summary>
    public enum HTableSize
    {
        Small = 0,
        Default = 1,
        Large = 2
    }

    /// <summary>
    /// 表格列类型枚举
    /// </summary>
    public enum HTableColumnType
    {
        Text = 0,
        CheckBox = 1,
        Template = 2
    }

    /// <summary>
    /// 表格边框样式枚举
    /// </summary>
    public enum HTableBorderStyle
    {
        None = 0,
        Horizontal = 1,
        Vertical = 2,
        All = 3
    }

    /// <summary>
    /// 表格合计项
    /// </summary>
    public class HTableSummaryItem
    {
        /// <summary>
        /// 内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 宽度
        /// </summary>
        public double Width { get; set; }

        /// <summary>
        /// 排序索引
        /// </summary>
        public int Index { get; set; }
    }
}
