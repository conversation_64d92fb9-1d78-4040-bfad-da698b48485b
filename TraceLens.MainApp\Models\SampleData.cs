using System;
using System.Collections.ObjectModel;

namespace TraceLens.MainApp.Models
{
    /// <summary>
    /// 示例数据模型
    /// </summary>
    public class SampleDataItem
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public DateTime CreateTime { get; set; }
        public bool IsActive { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// 示例数据生成器
    /// </summary>
    public static class SampleDataGenerator
    {
        private static readonly string[] Names = {
            "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十",
            "郑十一", "王十二", "冯十三", "陈十四", "褚十五", "卫十六"
        };

        private static readonly string[] Departments = {
            "技术部", "产品部", "运营部", "市场部", "人事部", "财务部", "法务部"
        };

        private static readonly string[] Statuses = {
            "正常", "待审核", "已禁用", "已删除"
        };

        public static ObservableCollection<SampleDataItem> GenerateData(int count = 20)
        {
            var random = new Random();
            var data = new ObservableCollection<SampleDataItem>();

            for (int i = 1; i <= count; i++)
            {
                data.Add(new SampleDataItem
                {
                    Id = i,
                    Name = Names[random.Next(Names.Length)],
                    Email = $"user{i}@example.com",
                    Department = Departments[random.Next(Departments.Length)],
                    CreateTime = DateTime.Now.AddDays(-random.Next(365)),
                    IsActive = random.Next(2) == 1,
                    Status = Statuses[random.Next(Statuses.Length)]
                });
            }

            return data;
        }
    }
}
