<Window x:Class="TraceLens.MainApp.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:prism="http://prismlibrary.com/"
        xmlns:local="clr-namespace:TraceLens.MainApp.Views"
        xmlns:HControl="clr-namespace:TraceLens.ThemeControl.Controls;assembly=TraceLens.ThemeControl"
        prism:ViewModelLocator.AutoWireViewModel="True"
        mc:Ignorable="d"
        Title="TraceLens 控件演示" Height="600" Width="1000">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0"
                   Text="TraceLens 控件演示"
                   FontSize="24"
                   FontWeight="Bold"
                   HorizontalAlignment="Center"
                   Margin="20"/>

        <!-- 内容区域 -->
        <StackPanel Grid.Row="1" Margin="20">
            <HControl:HTable></HControl:HTable>

        </StackPanel>
    </Grid>
</Window>
