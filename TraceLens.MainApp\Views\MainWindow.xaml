<Window x:Class="TraceLens.MainApp.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:prism="http://prismlibrary.com/"
        xmlns:local="clr-namespace:TraceLens.MainApp.Views"
        xmlns:HControl="clr-namespace:TraceLens.ThemeControl.Controls;assembly=TraceLens.ThemeControl"
        prism:ViewModelLocator.AutoWireViewModel="True"
        mc:Ignorable="d"
        Title="TraceLens HTable 控件演示" Height="800" Width="1400"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <Border Grid.Row="0"
                Background="{DynamicResource PrimaryBrush}"
                Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <HControl:HIcon IconName="table"
                               IconSize="28"
                               IconColor="White"
                               Margin="0,0,10,0"/>
                <TextBlock Text="TraceLens HTable 控件演示"
                           FontSize="24"
                           FontWeight="Bold"
                           Foreground="White"
                           VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- 工具栏 -->
        <Border Grid.Row="1"
                Background="{DynamicResource FillExtraLightBrush}"
                BorderBrush="{DynamicResource BorderBaseBrush}"
                BorderThickness="0,0,0,1"
                Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                <Button Content="刷新数据"
                        Command="{Binding RefreshCommand}"
                        Style="{DynamicResource PrimaryButton}"
                        Margin="0,0,10,0">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{DynamicResource PrimaryBrush}"
                                    CornerRadius="4"
                                    Padding="15,8">
                                <StackPanel Orientation="Horizontal">
                                    <HControl:HIcon IconName="refresh"
                                                   IconSize="16"
                                                   IconColor="White"
                                                   Margin="0,0,5,0"/>
                                    <TextBlock Text="{TemplateBinding Content}"
                                              Foreground="White"
                                              FontWeight="Medium"/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Opacity" Value="0.8"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <Button Content="添加数据"
                        Command="{Binding AddCommand}"
                        Margin="0,0,10,0">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{DynamicResource SuccessBrush}"
                                    CornerRadius="4"
                                    Padding="15,8">
                                <StackPanel Orientation="Horizontal">
                                    <HControl:HIcon IconName="plus"
                                                   IconSize="16"
                                                   IconColor="White"
                                                   Margin="0,0,5,0"/>
                                    <TextBlock Text="{TemplateBinding Content}"
                                              Foreground="White"
                                              FontWeight="Medium"/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Opacity" Value="0.8"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>

                <Button Content="删除选中"
                        Command="{Binding DeleteCommand}">
                    <Button.Template>
                        <ControlTemplate TargetType="Button">
                            <Border Background="{DynamicResource DangerBrush}"
                                    CornerRadius="4"
                                    Padding="15,8">
                                <StackPanel Orientation="Horizontal">
                                    <HControl:HIcon IconName="trash"
                                                   IconSize="16"
                                                   IconColor="White"
                                                   Margin="0,0,5,0"/>
                                    <TextBlock Text="{TemplateBinding Content}"
                                              Foreground="White"
                                              FontWeight="Medium"/>
                                </StackPanel>
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Opacity" Value="0.8"/>
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter Property="Opacity" Value="0.5"/>
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                </Button>
            </StackPanel>
        </Border>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="2" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 表格标题和统计信息 -->
            <Border Grid.Row="0"
                    Background="{DynamicResource FillLightBrush}"
                    BorderBrush="{DynamicResource BorderBaseBrush}"
                    BorderThickness="1,1,1,0"
                    CornerRadius="6,6,0,0"
                    Padding="20,15">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <TextBlock Text="用户数据表"
                                  FontSize="18"
                                  FontWeight="Medium"
                                  Foreground="{DynamicResource TextRegularBrush}"
                                  VerticalAlignment="Center"/>
                        <Border Background="{DynamicResource InfoBrush}"
                               CornerRadius="12"
                               Padding="8,4"
                               Margin="15,0,0,0">
                            <TextBlock Text="{Binding SampleData.Count, StringFormat='共 {0} 条记录'}"
                                      FontSize="12"
                                      Foreground="White"/>
                        </Border>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <TextBlock Text="选中项: "
                                  FontSize="14"
                                  Foreground="{DynamicResource TextSecondaryBrush}"
                                  VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding SelectedItem.Name, FallbackValue='无'}"
                                  FontSize="14"
                                  FontWeight="Medium"
                                  Foreground="{DynamicResource PrimaryBrush}"
                                  VerticalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- HTable 控件 -->
            <HControl:HTable Grid.Row="1"
                            ItemsSource="{Binding SampleData}"
                            Columns="{Binding TableColumns}"
                            SelectedItem="{Binding SelectedItem, Mode=TwoWay}"
                            IsLoading="{Binding IsLoading}"
                            LoadingText="正在加载数据..."
                            EmptyText="暂无数据，点击上方按钮添加数据"
                            ShowBorder="True"
                            ShowStripe="True"
                            ShowSummary="False"
                            TableSize="Default"
                            RowHeight="52"
                            HeaderHeight="48"
                            BorderStyle="All"/>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="3"
                Background="{DynamicResource FillExtraLightBrush}"
                BorderBrush="{DynamicResource BorderBaseBrush}"
                BorderThickness="0,1,0,0"
                Padding="20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                          Text="TraceLens HTable 控件演示 - 支持排序、选择、加载状态等功能"
                          FontSize="12"
                          Foreground="{DynamicResource TextSecondaryBrush}"
                          VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="版本: "
                              FontSize="12"
                              Foreground="{DynamicResource TextSecondaryBrush}"
                              VerticalAlignment="Center"/>
                    <TextBlock Text="1.0.0"
                              FontSize="12"
                              FontWeight="Medium"
                              Foreground="{DynamicResource PrimaryBrush}"
                              VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
