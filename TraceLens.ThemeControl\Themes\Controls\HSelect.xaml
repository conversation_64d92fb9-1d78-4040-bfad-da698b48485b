<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:TraceLens.ThemeControl.Controls"
                    xmlns:hicon="clr-namespace:TraceLens.ThemeControl.Controls"
                    xmlns:converters="clr-namespace:TraceLens.ThemeControl.Converters">

    <!-- HSelect 下拉项样式 -->
    <Style x:Key="HSelectItemStyle" TargetType="ListBoxItem">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListBoxItem">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource FillLightBrush}"/>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Background" Value="{DynamicResource PrimaryBrush}"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- HSelect 图标按钮样式 -->
    <Style x:Key="HSelectIconButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="2">
                        <ContentPresenter HorizontalAlignment="Center"
                                        VerticalAlignment="Center"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource FillLightBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" Value="{DynamicResource FillBaseBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- HSelect 控件样式 -->
    <Style TargetType="{x:Type local:HSelect}">
        <Setter Property="Background" Value="{DynamicResource FillExtraLightBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBaseBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextRegularBrush}"/>
        <Setter Property="BorderThickness" Value="{DynamicResource BorderWidthBase}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSizeBase}"/>
        <Setter Property="FontFamily" Value="{DynamicResource IndustrialFont}"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Height" Value="38"/>
        <Setter Property="MinWidth" Value="120"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:HSelect}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 标签 -->
                        <TextBlock x:Name="PART_Label"
                                  Grid.Column="0"
                                  Grid.Row="1"
                                  Text="{TemplateBinding Label}"
                                  FontSize="{TemplateBinding FontSize}"
                                  FontFamily="{TemplateBinding FontFamily}"
                                  Foreground="{DynamicResource TextRegularBrush}"
                                  VerticalAlignment="Center"
                                  HorizontalAlignment="Right"
                                  Margin="0,0,8,0"
                                  Width="{TemplateBinding LabelWidth}"
                                  Visibility="Collapsed"/>

                        <!-- 选择框容器 -->
                        <Grid Grid.Column="1" Grid.Row="1">
                        <!-- 主输入区域 -->
                        <Border x:Name="PART_Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{DynamicResource BorderRadiusBase}"
                                SnapsToDevicePixels="True">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- 内容区域 -->
                                <Grid Grid.Column="0" Margin="{TemplateBinding Padding}">
                                    <!-- 占位符文本 -->
                                    <TextBlock x:Name="PART_Placeholder"
                                              Text="{TemplateBinding Placeholder}"
                                              Foreground="{DynamicResource TextPlaceholderBrush}"
                                              FontSize="{TemplateBinding FontSize}"
                                              FontFamily="{TemplateBinding FontFamily}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              IsHitTestVisible="False"
                                              Visibility="Visible"/>

                                    <!-- 选中文本 -->
                                    <TextBlock x:Name="PART_SelectedText"
                                              Text="{TemplateBinding SelectedText}"
                                              Foreground="{TemplateBinding Foreground}"
                                              FontSize="{TemplateBinding FontSize}"
                                              FontFamily="{TemplateBinding FontFamily}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              IsHitTestVisible="False"
                                              Visibility="Collapsed"/>

                                    <!-- 过滤输入框 -->
                                    <TextBox x:Name="PART_FilterTextBox"
                                            FontSize="{TemplateBinding FontSize}"
                                            FontFamily="{TemplateBinding FontFamily}"
                                            Foreground="{TemplateBinding Foreground}"
                                            Background="Transparent"
                                            BorderThickness="0"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                            HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            CaretBrush="{DynamicResource PrimaryBrush}"
                                            Visibility="Collapsed"/>
                                </Grid>

                                <!-- 右侧按钮区域 -->
                                <StackPanel Grid.Column="1"
                                           Orientation="Horizontal"
                                           VerticalAlignment="Center">

                                    <!-- 清除按钮 -->
                                    <Button x:Name="PART_ClearButton"
                                           Width="16"
                                           Height="16"
                                           Margin="4,0"
                                           Background="Transparent"
                                           BorderThickness="0"
                                           Cursor="Hand"
                                           Visibility="Collapsed"
                                           ToolTip="清除"
                                           Style="{StaticResource HSelectIconButtonStyle}">
                                        <hicon:HIcon IconName="times-circle"
                                                    IconSize="{DynamicResource IconSizeSmall}"
                                                    IconColor="{DynamicResource TextSecondaryBrush}"/>
                                    </Button>

                                    <!-- 下拉按钮 -->
                                    <Button x:Name="PART_DropDownButton"
                                           Width="16"
                                           Height="16"
                                           Margin="4,0,8,0"
                                           Background="Transparent"
                                           BorderThickness="0"
                                           Cursor="Hand"
                                           Style="{StaticResource HSelectIconButtonStyle}">
                                        <hicon:HIcon x:Name="PART_DropDownIcon"
                                                    IconName="chevron-down"
                                                    IconSize="{DynamicResource IconSizeSmall}"
                                                    IconColor="{DynamicResource TextSecondaryBrush}"/>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <!-- 下拉弹出框 -->
                        <Popup x:Name="PART_Popup"
                               IsOpen="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}"
                               PlacementTarget="{Binding ElementName=PART_Border}"
                               Placement="Bottom"
                               AllowsTransparency="True"
                               PopupAnimation="Slide"
                               StaysOpen="False">
                            <Border Background="{DynamicResource FillExtraLightBrush}"
                                    BorderBrush="{DynamicResource BorderBaseBrush}"
                                    BorderThickness="{DynamicResource BorderWidthBase}"
                                    CornerRadius="{DynamicResource BorderRadiusBase}"
                                    MinWidth="{Binding ActualWidth, ElementName=PART_Border}"
                                    MaxHeight="{TemplateBinding MaxDropDownHeight}"
                                    Effect="{DynamicResource DropShadowEffect}">
                                <ListBox x:Name="PART_ListBox"
                                        ItemsSource="{TemplateBinding ItemsSource}"
                                        DisplayMemberPath="{TemplateBinding DisplayMemberPath}"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                                        ItemContainerStyle="{StaticResource HSelectItemStyle}"/>
                            </Border>
                        </Popup>
                        </Grid>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!-- 标签显示 -->
                        <DataTrigger Binding="{Binding Label, RelativeSource={RelativeSource Self}, Converter={x:Static converters:StringToVisibilityConverter.Instance}}" Value="Visible">
                            <Setter TargetName="PART_Label" Property="Visibility" Value="Visible"/>
                        </DataTrigger>

                        <!-- 显示冒号 -->
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Label, RelativeSource={RelativeSource Self}, Converter={x:Static converters:StringToVisibilityConverter.Instance}}" Value="Visible"/>
                                <Condition Binding="{Binding ShowColon, RelativeSource={RelativeSource Self}}" Value="True"/>
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Label" Property="Text" Value="{Binding Label, RelativeSource={RelativeSource TemplatedParent}, StringFormat='{}{0}:'}"/>
                        </MultiDataTrigger>

                        <!-- 占位符显示/隐藏 -->
                        <DataTrigger Binding="{Binding SelectedText, RelativeSource={RelativeSource Self}, Converter={x:Static converters:InvertedStringToVisibilityConverter.Instance}}" Value="Collapsed">
                            <Setter TargetName="PART_Placeholder" Property="Visibility" Value="Collapsed"/>
                        </DataTrigger>

                        <!-- 选中文本显示/隐藏 -->
                        <DataTrigger Binding="{Binding SelectedText, RelativeSource={RelativeSource Self}, Converter={x:Static converters:StringToVisibilityConverter.Instance}}" Value="Visible">
                            <Setter TargetName="PART_SelectedText" Property="Visibility" Value="Visible"/>
                        </DataTrigger>

                        <!-- 可搜索模式 -->
                        <Trigger Property="IsFilterable" Value="True">
                            <Setter TargetName="PART_FilterTextBox" Property="Visibility" Value="Visible"/>
                            <Setter TargetName="PART_SelectedText" Property="Visibility" Value="Collapsed"/>
                        </Trigger>

                        <!-- 下拉框展开状态 -->
                        <Trigger Property="IsDropDownOpen" Value="True">
                            <Setter TargetName="PART_DropDownIcon" Property="IconName" Value="chevron-up"/>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
                        </Trigger>

                        <!-- 鼠标悬停状态 -->
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{DynamicResource BorderLightBrush}"/>
                        </Trigger>

                        <!-- 聚焦状态 -->
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsKeyboardFocusWithin" Value="True"/>
                                <Condition Property="IsDisabled" Value="False"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{DynamicResource PrimaryBrush}"/>
                        </MultiTrigger>

                        <!-- 禁用状态 -->
                        <Trigger Property="IsDisabled" Value="True">
                            <Setter Property="Background" Value="{DynamicResource FillLightBrush}"/>
                            <Setter Property="Foreground" Value="{DynamicResource TextDisabledBrush}"/>
                            <Setter TargetName="PART_Border" Property="BorderBrush" Value="{DynamicResource BorderLighterBrush}"/>
                            <Setter TargetName="PART_FilterTextBox" Property="IsEnabled" Value="False"/>
                            <Setter Property="Cursor" Value="Arrow"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

        <!-- 尺寸样式 -->
        <Style.Triggers>
            <!-- 小尺寸 -->
            <Trigger Property="Size" Value="Small">
                <Setter Property="Height" Value="24"/>
                <Setter Property="FontSize" Value="{DynamicResource FontSizeExtraSmall}"/>
                <Setter Property="Padding" Value="8,4"/>
            </Trigger>

            <!-- 大尺寸 -->
            <Trigger Property="Size" Value="Large">
                <Setter Property="Height" Value="40"/>
                <Setter Property="FontSize" Value="{DynamicResource FontSizeMedium}"/>
                <Setter Property="Padding" Value="16,12"/>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
