using System;
using System.Collections;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Input;
using System.Windows.Media;

namespace TraceLens.ThemeControl.Controls
{
    /// <summary>
    /// HSelect - 仿照 Element UI 的下拉选择框控件
    /// 支持多种尺寸、状态和功能特性
    /// </summary>
    public class HSelect : Control
    {
        #region 依赖属性

        /// <summary>
        /// 选中的值
        /// </summary>
        public static readonly DependencyProperty SelectedValueProperty =
            DependencyProperty.Register(nameof(SelectedValue), typeof(object), typeof(HSelect),
                new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnSelectedValueChanged));

        /// <summary>
        /// 显示的文本
        /// </summary>
        public static readonly DependencyProperty SelectedTextProperty =
            DependencyProperty.Register(nameof(SelectedText), typeof(string), typeof(HSelect),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 占位符文本
        /// </summary>
        public static readonly DependencyProperty PlaceholderProperty =
            DependencyProperty.Register(nameof(Placeholder), typeof(string), typeof(HSelect),
                new PropertyMetadata("请选择"));

        /// <summary>
        /// 是否禁用
        /// </summary>
        public static readonly DependencyProperty IsDisabledProperty =
            DependencyProperty.Register(nameof(IsDisabled), typeof(bool), typeof(HSelect),
                new PropertyMetadata(false, OnIsDisabledChanged));

        /// <summary>
        /// 控件尺寸
        /// </summary>
        public static readonly DependencyProperty SizeProperty =
            DependencyProperty.Register(nameof(Size), typeof(HSelectSize), typeof(HSelect),
                new PropertyMetadata(HSelectSize.Default, OnSizeChanged));

        /// <summary>
        /// 是否可清除
        /// </summary>
        public static readonly DependencyProperty IsClearableProperty =
            DependencyProperty.Register(nameof(IsClearable), typeof(bool), typeof(HSelect),
                new PropertyMetadata(false));

        /// <summary>
        /// 是否可搜索
        /// </summary>
        public static readonly DependencyProperty IsFilterableProperty =
            DependencyProperty.Register(nameof(IsFilterable), typeof(bool), typeof(HSelect),
                new PropertyMetadata(false));

        /// <summary>
        /// 数据源
        /// </summary>
        public static readonly DependencyProperty ItemsSourceProperty =
            DependencyProperty.Register(nameof(ItemsSource), typeof(IEnumerable), typeof(HSelect),
                new PropertyMetadata(null, OnItemsSourceChanged));

        /// <summary>
        /// 显示成员路径
        /// </summary>
        public static readonly DependencyProperty DisplayMemberPathProperty =
            DependencyProperty.Register(nameof(DisplayMemberPath), typeof(string), typeof(HSelect),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 值成员路径
        /// </summary>
        public static readonly DependencyProperty SelectedValuePathProperty =
            DependencyProperty.Register(nameof(SelectedValuePath), typeof(string), typeof(HSelect),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 下拉框最大高度
        /// </summary>
        public static readonly DependencyProperty MaxDropDownHeightProperty =
            DependencyProperty.Register(nameof(MaxDropDownHeight), typeof(double), typeof(HSelect),
                new PropertyMetadata(200.0));

        /// <summary>
        /// 是否展开下拉框
        /// </summary>
        public static readonly DependencyProperty IsDropDownOpenProperty =
            DependencyProperty.Register(nameof(IsDropDownOpen), typeof(bool), typeof(HSelect),
                new PropertyMetadata(false, OnIsDropDownOpenChanged));

        /// <summary>
        /// 标签文本
        /// </summary>
        public static readonly DependencyProperty LabelProperty =
            DependencyProperty.Register(nameof(Label), typeof(string), typeof(HSelect),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 标签宽度
        /// </summary>
        public static readonly DependencyProperty LabelWidthProperty =
            DependencyProperty.Register(nameof(LabelWidth), typeof(double), typeof(HSelect),
                new PropertyMetadata(80.0));

        /// <summary>
        /// 标签位置
        /// </summary>
        public static readonly DependencyProperty LabelPositionProperty =
            DependencyProperty.Register(nameof(LabelPosition), typeof(HLabelPosition), typeof(HSelect),
                new PropertyMetadata(HLabelPosition.Left));

        /// <summary>
        /// 是否显示冒号
        /// </summary>
        public static readonly DependencyProperty ShowColonProperty =
            DependencyProperty.Register(nameof(ShowColon), typeof(bool), typeof(HSelect),
                new PropertyMetadata(true));

        #endregion

        #region 属性

        /// <summary>
        /// 选中的值
        /// </summary>
        public object SelectedValue
        {
            get => GetValue(SelectedValueProperty);
            set => SetValue(SelectedValueProperty, value);
        }

        /// <summary>
        /// 显示的文本
        /// </summary>
        public string SelectedText
        {
            get => (string)GetValue(SelectedTextProperty);
            set => SetValue(SelectedTextProperty, value);
        }

        /// <summary>
        /// 占位符文本
        /// </summary>
        public string Placeholder
        {
            get => (string)GetValue(PlaceholderProperty);
            set => SetValue(PlaceholderProperty, value);
        }

        /// <summary>
        /// 是否禁用
        /// </summary>
        public bool IsDisabled
        {
            get => (bool)GetValue(IsDisabledProperty);
            set => SetValue(IsDisabledProperty, value);
        }

        /// <summary>
        /// 控件尺寸
        /// </summary>
        public HSelectSize Size
        {
            get => (HSelectSize)GetValue(SizeProperty);
            set => SetValue(SizeProperty, value);
        }

        /// <summary>
        /// 是否可清除
        /// </summary>
        public bool IsClearable
        {
            get => (bool)GetValue(IsClearableProperty);
            set => SetValue(IsClearableProperty, value);
        }

        /// <summary>
        /// 是否可搜索
        /// </summary>
        public bool IsFilterable
        {
            get => (bool)GetValue(IsFilterableProperty);
            set => SetValue(IsFilterableProperty, value);
        }

        /// <summary>
        /// 数据源
        /// </summary>
        public IEnumerable ItemsSource
        {
            get => (IEnumerable)GetValue(ItemsSourceProperty);
            set => SetValue(ItemsSourceProperty, value);
        }

        /// <summary>
        /// 显示成员路径
        /// </summary>
        public string DisplayMemberPath
        {
            get => (string)GetValue(DisplayMemberPathProperty);
            set => SetValue(DisplayMemberPathProperty, value);
        }

        /// <summary>
        /// 值成员路径
        /// </summary>
        public string SelectedValuePath
        {
            get => (string)GetValue(SelectedValuePathProperty);
            set => SetValue(SelectedValuePathProperty, value);
        }

        /// <summary>
        /// 下拉框最大高度
        /// </summary>
        public double MaxDropDownHeight
        {
            get => (double)GetValue(MaxDropDownHeightProperty);
            set => SetValue(MaxDropDownHeightProperty, value);
        }

        /// <summary>
        /// 是否展开下拉框
        /// </summary>
        public bool IsDropDownOpen
        {
            get => (bool)GetValue(IsDropDownOpenProperty);
            set => SetValue(IsDropDownOpenProperty, value);
        }

        /// <summary>
        /// 标签文本
        /// </summary>
        public string Label
        {
            get => (string)GetValue(LabelProperty);
            set => SetValue(LabelProperty, value);
        }

        /// <summary>
        /// 标签宽度
        /// </summary>
        public double LabelWidth
        {
            get => (double)GetValue(LabelWidthProperty);
            set => SetValue(LabelWidthProperty, value);
        }

        /// <summary>
        /// 标签位置
        /// </summary>
        public HLabelPosition LabelPosition
        {
            get => (HLabelPosition)GetValue(LabelPositionProperty);
            set => SetValue(LabelPositionProperty, value);
        }

        /// <summary>
        /// 是否显示冒号
        /// </summary>
        public bool ShowColon
        {
            get => (bool)GetValue(ShowColonProperty);
            set => SetValue(ShowColonProperty, value);
        }

        #endregion

        #region 路由事件

        /// <summary>
        /// 选择改变事件
        /// </summary>
        public static readonly RoutedEvent SelectionChangedEvent =
            EventManager.RegisterRoutedEvent(nameof(SelectionChanged), RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(HSelect));

        /// <summary>
        /// 清除事件
        /// </summary>
        public static readonly RoutedEvent ClearEvent =
            EventManager.RegisterRoutedEvent(nameof(Clear), RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(HSelect));

        /// <summary>
        /// 下拉框打开事件
        /// </summary>
        public static readonly RoutedEvent DropDownOpenedEvent =
            EventManager.RegisterRoutedEvent(nameof(DropDownOpened), RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(HSelect));

        /// <summary>
        /// 下拉框关闭事件
        /// </summary>
        public static readonly RoutedEvent DropDownClosedEvent =
            EventManager.RegisterRoutedEvent(nameof(DropDownClosed), RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(HSelect));

        /// <summary>
        /// 选择改变事件
        /// </summary>
        public event RoutedEventHandler SelectionChanged
        {
            add => AddHandler(SelectionChangedEvent, value);
            remove => RemoveHandler(SelectionChangedEvent, value);
        }

        /// <summary>
        /// 清除事件
        /// </summary>
        public event RoutedEventHandler Clear
        {
            add => AddHandler(ClearEvent, value);
            remove => RemoveHandler(ClearEvent, value);
        }

        /// <summary>
        /// 下拉框打开事件
        /// </summary>
        public event RoutedEventHandler DropDownOpened
        {
            add => AddHandler(DropDownOpenedEvent, value);
            remove => RemoveHandler(DropDownOpenedEvent, value);
        }

        /// <summary>
        /// 下拉框关闭事件
        /// </summary>
        public event RoutedEventHandler DropDownClosed
        {
            add => AddHandler(DropDownClosedEvent, value);
            remove => RemoveHandler(DropDownClosedEvent, value);
        }

        #endregion

        #region 私有字段

        private TextBox? _filterTextBox = null;
        private Button? _clearButton = null;
        private Button? _dropDownButton = null;
        private Popup? _popup = null;
        private ListBox? _listBox = null;
        private TextBlock? _selectedTextBlock = null;
        private TextBlock? _placeholderBlock = null;

        #endregion

        #region 构造函数

        static HSelect()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(HSelect), new FrameworkPropertyMetadata(typeof(HSelect)));
        }

        public HSelect()
        {
            // 设置默认值
            SelectedValue = null;
            SelectedText = string.Empty;
            Placeholder = "请选择";
            IsDisabled = false;
            Size = HSelectSize.Default;
            IsClearable = false;
            IsFilterable = false;
            ItemsSource = null;
            DisplayMemberPath = string.Empty;
            SelectedValuePath = string.Empty;
            MaxDropDownHeight = 200.0;
            IsDropDownOpen = false;
            Label = string.Empty;
            LabelWidth = 80.0;
            LabelPosition = HLabelPosition.Left;
            ShowColon = true;

            // 设置控件默认属性
            IsTabStop = true;
            Focusable = true;

            Loaded += OnLoaded;
        }

        #endregion

        #region 重写方法

        public override void OnApplyTemplate()
        {
            base.OnApplyTemplate();

            // 解绑旧事件
            UnbindEvents();

            // 获取模板中的控件
            _filterTextBox = GetTemplateChild("PART_FilterTextBox") as TextBox;
            _clearButton = GetTemplateChild("PART_ClearButton") as Button;
            _dropDownButton = GetTemplateChild("PART_DropDownButton") as Button;
            _popup = GetTemplateChild("PART_Popup") as Popup;
            _listBox = GetTemplateChild("PART_ListBox") as ListBox;
            _selectedTextBlock = GetTemplateChild("PART_SelectedText") as TextBlock;
            _placeholderBlock = GetTemplateChild("PART_Placeholder") as TextBlock;

            // 绑定事件
            BindEvents();

            UpdateVisualState();
            UpdateSelectedText();
        }

        private void BindEvents()
        {
            if (_filterTextBox != null)
            {
                _filterTextBox.TextChanged += OnFilterTextChanged;
                _filterTextBox.GotFocus += OnFilterTextBoxGotFocus;
                _filterTextBox.LostFocus += OnFilterTextBoxLostFocus;
                _filterTextBox.KeyDown += OnFilterTextBoxKeyDown;
            }

            if (_clearButton != null)
            {
                _clearButton.Click += OnClearButtonClick;
            }

            if (_dropDownButton != null)
            {
                _dropDownButton.Click += OnDropDownButtonClick;
            }

            if (_popup != null)
            {
                _popup.Opened += OnPopupOpened;
                _popup.Closed += OnPopupClosed;
            }

            if (_listBox != null)
            {
                _listBox.SelectionChanged += OnListBoxSelectionChanged;
                _listBox.MouseDoubleClick += OnListBoxMouseDoubleClick;
            }

            MouseLeftButtonDown += OnMouseLeftButtonDown;
            KeyDown += OnKeyDown;
        }

        private void UnbindEvents()
        {
            if (_filterTextBox != null)
            {
                _filterTextBox.TextChanged -= OnFilterTextChanged;
                _filterTextBox.GotFocus -= OnFilterTextBoxGotFocus;
                _filterTextBox.LostFocus -= OnFilterTextBoxLostFocus;
                _filterTextBox.KeyDown -= OnFilterTextBoxKeyDown;
            }

            if (_clearButton != null)
            {
                _clearButton.Click -= OnClearButtonClick;
            }

            if (_dropDownButton != null)
            {
                _dropDownButton.Click -= OnDropDownButtonClick;
            }

            if (_popup != null)
            {
                _popup.Opened -= OnPopupOpened;
                _popup.Closed -= OnPopupClosed;
            }

            if (_listBox != null)
            {
                _listBox.SelectionChanged -= OnListBoxSelectionChanged;
                _listBox.MouseDoubleClick -= OnListBoxMouseDoubleClick;
            }

            MouseLeftButtonDown -= OnMouseLeftButtonDown;
            KeyDown -= OnKeyDown;
        }

        #endregion

        #region 私有方法

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            UpdateVisualState();
        }

        private static void OnSelectedValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HSelect select)
            {
                select.OnSelectedValueChanged();
            }
        }

        private static void OnIsDisabledChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HSelect select)
            {
                select.UpdateVisualState();
            }
        }

        private static void OnSizeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HSelect select)
            {
                select.UpdateVisualState();
            }
        }

        private static void OnItemsSourceChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HSelect select)
            {
                select.OnItemsSourceChanged();
            }
        }

        private static void OnIsDropDownOpenChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HSelect select)
            {
                select.OnIsDropDownOpenChanged();
            }
        }

        private void OnSelectedValueChanged()
        {
            UpdateSelectedText();
            RaiseEvent(new RoutedEventArgs(SelectionChangedEvent));
        }

        private void OnItemsSourceChanged()
        {
            if (_listBox != null)
            {
                _listBox.ItemsSource = ItemsSource;
            }
        }

        private void OnIsDropDownOpenChanged()
        {
            if (_popup != null)
            {
                _popup.IsOpen = IsDropDownOpen;
            }
        }

        private void UpdateSelectedText()
        {
            if (SelectedValue == null)
            {
                SelectedText = string.Empty;
                return;
            }

            if (!string.IsNullOrEmpty(DisplayMemberPath))
            {
                var property = SelectedValue.GetType().GetProperty(DisplayMemberPath);
                if (property != null)
                {
                    SelectedText = property.GetValue(SelectedValue)?.ToString() ?? string.Empty;
                    return;
                }
            }

            SelectedText = SelectedValue.ToString() ?? string.Empty;
        }

        private void OnFilterTextChanged(object sender, TextChangedEventArgs e)
        {
            if (!IsFilterable || _listBox == null) return;

            var filterText = _filterTextBox?.Text?.ToLower() ?? string.Empty;

            // 简单的文本过滤实现
            if (string.IsNullOrEmpty(filterText))
            {
                _listBox.ItemsSource = ItemsSource;
            }
            else
            {
                // 这里可以实现更复杂的过滤逻辑
                _listBox.ItemsSource = ItemsSource;
            }
        }

        private void OnFilterTextBoxGotFocus(object sender, RoutedEventArgs e)
        {
            IsDropDownOpen = true;
        }

        private void OnFilterTextBoxLostFocus(object sender, RoutedEventArgs e)
        {
            // 延迟关闭，允许点击下拉项
            Dispatcher.BeginInvoke(new Action(() =>
            {
                if (!IsKeyboardFocusWithin)
                {
                    IsDropDownOpen = false;
                }
            }), System.Windows.Threading.DispatcherPriority.Background);
        }

        private void OnFilterTextBoxKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                IsDropDownOpen = false;
                Focus();
            }
            else if (e.Key == Key.Enter && _listBox?.SelectedItem != null)
            {
                SelectItem(_listBox.SelectedItem);
                IsDropDownOpen = false;
            }
            else if (e.Key == Key.Down && _listBox != null)
            {
                if (_listBox.SelectedIndex < _listBox.Items.Count - 1)
                {
                    _listBox.SelectedIndex++;
                }
                e.Handled = true;
            }
            else if (e.Key == Key.Up && _listBox != null)
            {
                if (_listBox.SelectedIndex > 0)
                {
                    _listBox.SelectedIndex--;
                }
                e.Handled = true;
            }
        }

        private void OnClearButtonClick(object sender, RoutedEventArgs e)
        {
            SelectedValue = null;
            SelectedText = string.Empty;
            RaiseEvent(new RoutedEventArgs(ClearEvent));
            Focus();
        }

        private void OnDropDownButtonClick(object sender, RoutedEventArgs e)
        {
            IsDropDownOpen = !IsDropDownOpen;
        }

        private void OnMouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!IsDisabled)
            {
                IsDropDownOpen = !IsDropDownOpen;
                Focus();
            }
        }

        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Space || e.Key == Key.Enter)
            {
                IsDropDownOpen = !IsDropDownOpen;
                e.Handled = true;
            }
            else if (e.Key == Key.Escape)
            {
                IsDropDownOpen = false;
                e.Handled = true;
            }
        }

        private void OnPopupOpened(object sender, EventArgs e)
        {
            RaiseEvent(new RoutedEventArgs(DropDownOpenedEvent));
        }

        private void OnPopupClosed(object sender, EventArgs e)
        {
            RaiseEvent(new RoutedEventArgs(DropDownClosedEvent));
        }

        private void OnListBoxSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_listBox?.SelectedItem != null)
            {
                SelectItem(_listBox.SelectedItem);
            }
        }

        private void OnListBoxMouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (_listBox?.SelectedItem != null)
            {
                SelectItem(_listBox.SelectedItem);
                IsDropDownOpen = false;
            }
        }

        private void SelectItem(object item)
        {
            if (!string.IsNullOrEmpty(SelectedValuePath))
            {
                var property = item.GetType().GetProperty(SelectedValuePath);
                if (property != null)
                {
                    SelectedValue = property.GetValue(item);
                    return;
                }
            }

            SelectedValue = item;
        }

        private void UpdateVisualState()
        {
            if (IsDisabled)
            {
                VisualStateManager.GoToState(this, "Disabled", true);
            }
            else
            {
                VisualStateManager.GoToState(this, "Normal", true);
            }

            // 更新尺寸状态
            VisualStateManager.GoToState(this, Size.ToString(), true);

            // 更新清除按钮可见性
            if (_clearButton != null)
            {
                _clearButton.Visibility = IsClearable && SelectedValue != null && !IsDisabled ? Visibility.Visible : Visibility.Collapsed;
            }

            // 更新占位符可见性
            if (_placeholderBlock != null)
            {
                _placeholderBlock.Visibility = string.IsNullOrEmpty(SelectedText) ? Visibility.Visible : Visibility.Collapsed;
            }

            // 更新选中文本可见性
            if (_selectedTextBlock != null)
            {
                _selectedTextBlock.Visibility = !string.IsNullOrEmpty(SelectedText) ? Visibility.Visible : Visibility.Collapsed;
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 聚焦到控件
        /// </summary>
        public new void Focus()
        {
            if (IsFilterable && _filterTextBox != null)
            {
                _filterTextBox.Focus();
            }
            else
            {
                base.Focus();
            }
        }

        /// <summary>
        /// 清除选择
        /// </summary>
        public void ClearSelection()
        {
            SelectedValue = null;
            SelectedText = string.Empty;
            RaiseEvent(new RoutedEventArgs(ClearEvent));
        }

        #endregion
    }

    /// <summary>
    /// 下拉框尺寸枚举
    /// </summary>
    public enum HSelectSize
    {
        Small = 0,
        Default = 1,
        Large = 2
    }
}
