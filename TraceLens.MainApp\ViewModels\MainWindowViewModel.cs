using Prism.Mvvm;
using Prism.Commands;
using System.Windows.Input;
using System.Collections.ObjectModel;
using System.Windows.Controls;
using TraceLens.MainApp.Models;
using TraceLens.ThemeControl.Controls;

namespace TraceLens.MainApp.ViewModels
{
    public class MainWindowViewModel : ViewModelBase
    {
        private ObservableCollection<SampleDataItem> _sampleData = new();
        private ObservableCollection<HTableColumn> _tableColumns = new();
        private bool _isLoading;
        private SampleDataItem? _selectedItem;

        public MainWindowViewModel()
        {
            InitializeData();
            InitializeCommands();
        }

        #region 属性

        /// <summary>
        /// 示例数据
        /// </summary>
        public ObservableCollection<SampleDataItem> SampleData
        {
            get => _sampleData;
            set => SetProperty(ref _sampleData, value);
        }

        /// <summary>
        /// 表格列定义
        /// </summary>
        public ObservableCollection<HTableColumn> TableColumns
        {
            get => _tableColumns;
            set => SetProperty(ref _tableColumns, value);
        }

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// 选中的项
        /// </summary>
        public SampleDataItem? SelectedItem
        {
            get => _selectedItem;
            set => SetProperty(ref _selectedItem, value);
        }

        #endregion

        #region 命令

        public DelegateCommand RefreshCommand { get; private set; } = null!;
        public DelegateCommand AddCommand { get; private set; } = null!;
        public DelegateCommand DeleteCommand { get; private set; } = null!;

        #endregion

        #region 私有方法

        private void InitializeData()
        {
            // 初始化表格列
            _tableColumns = new ObservableCollection<HTableColumn>
            {
                new HTableColumn
                {
                    Header = "ID",
                    Property = "Id",
                    Width = new DataGridLength(80),
                    ColumnType = HTableColumnType.Text,
                    Sortable = true
                },
                new HTableColumn
                {
                    Header = "姓名",
                    Property = "Name",
                    Width = new DataGridLength(120),
                    ColumnType = HTableColumnType.Text,
                    Sortable = true
                },
                new HTableColumn
                {
                    Header = "邮箱",
                    Property = "Email",
                    Width = new DataGridLength(200),
                    ColumnType = HTableColumnType.Text,
                    Sortable = true
                },
                new HTableColumn
                {
                    Header = "部门",
                    Property = "Department",
                    Width = new DataGridLength(120),
                    ColumnType = HTableColumnType.Text,
                    Sortable = true
                },
                new HTableColumn
                {
                    Header = "创建时间",
                    Property = "CreateTime",
                    Width = new DataGridLength(150),
                    ColumnType = HTableColumnType.Text,
                    Sortable = true
                },
                new HTableColumn
                {
                    Header = "状态",
                    Property = "Status",
                    Width = new DataGridLength(100),
                    ColumnType = HTableColumnType.Text,
                    Sortable = true
                },
                new HTableColumn
                {
                    Header = "是否激活",
                    Property = "IsActive",
                    Width = new DataGridLength(100),
                    ColumnType = HTableColumnType.CheckBox,
                    Sortable = true
                }
            };

            // 生成示例数据
            _sampleData = SampleDataGenerator.GenerateData(50);
        }

        private void InitializeCommands()
        {
            RefreshCommand = new DelegateCommand(ExecuteRefresh);
            AddCommand = new DelegateCommand(ExecuteAdd);
            DeleteCommand = new DelegateCommand(ExecuteDelete, CanExecuteDelete);
        }

        private void ExecuteRefresh()
        {
            IsLoading = true;

            // 模拟异步加载
            Task.Run(async () =>
            {
                await Task.Delay(1000); // 模拟网络延迟

                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    SampleData = SampleDataGenerator.GenerateData(50);
                    IsLoading = false;
                });
            });
        }

        private void ExecuteAdd()
        {
            var newItem = new SampleDataItem
            {
                Id = SampleData.Count + 1,
                Name = "新用户",
                Email = $"newuser{SampleData.Count + 1}@example.com",
                Department = "技术部",
                CreateTime = DateTime.Now,
                IsActive = true,
                Status = "正常"
            };

            SampleData.Insert(0, newItem);
        }

        private void ExecuteDelete()
        {
            if (SelectedItem != null)
            {
                SampleData.Remove(SelectedItem);
                SelectedItem = null;
            }
        }

        private bool CanExecuteDelete()
        {
            return SelectedItem != null;
        }

        #endregion
    }
}
