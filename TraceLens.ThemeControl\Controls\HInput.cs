using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace TraceLens.ThemeControl.Controls
{
    /// <summary>
    /// HInput - 仿照 Element UI 的输入框控件
    /// 支持多种尺寸、状态和功能特性
    /// </summary>
    public class HInput : Control
    {
        #region 依赖属性

        /// <summary>
        /// 输入框的值
        /// </summary>
        public static readonly DependencyProperty TextProperty =
            DependencyProperty.Register(nameof(Text), typeof(string), typeof(HInput),
                new FrameworkPropertyMetadata(string.Empty, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnTextChanged));

        /// <summary>
        /// 占位符文本
        /// </summary>
        public static readonly DependencyProperty PlaceholderProperty =
            DependencyProperty.Register(nameof(Placeholder), typeof(string), typeof(HInput),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 是否禁用
        /// </summary>
        public static readonly DependencyProperty IsDisabledProperty =
            DependencyProperty.Register(nameof(IsDisabled), typeof(bool), typeof(HInput),
                new PropertyMetadata(false, OnIsDisabledChanged));

        /// <summary>
        /// 是否只读
        /// </summary>
        public static readonly DependencyProperty IsReadOnlyProperty =
            DependencyProperty.Register(nameof(IsReadOnly), typeof(bool), typeof(HInput),
                new PropertyMetadata(false));

        /// <summary>
        /// 控件尺寸
        /// </summary>
        public static readonly DependencyProperty SizeProperty =
            DependencyProperty.Register(nameof(Size), typeof(HInputSize), typeof(HInput),
                new PropertyMetadata(HInputSize.Default, OnSizeChanged));

        /// <summary>
        /// 是否可清除
        /// </summary>
        public static readonly DependencyProperty IsClearableProperty =
            DependencyProperty.Register(nameof(IsClearable), typeof(bool), typeof(HInput),
                new PropertyMetadata(false));

        /// <summary>
        /// 是否显示密码切换按钮
        /// </summary>
        public static readonly DependencyProperty ShowPasswordProperty =
            DependencyProperty.Register(nameof(ShowPassword), typeof(bool), typeof(HInput),
                new PropertyMetadata(false));

        /// <summary>
        /// 前缀图标
        /// </summary>
        public static readonly DependencyProperty PrefixIconProperty =
            DependencyProperty.Register(nameof(PrefixIcon), typeof(string), typeof(HInput),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 后缀图标
        /// </summary>
        public static readonly DependencyProperty SuffixIconProperty =
            DependencyProperty.Register(nameof(SuffixIcon), typeof(string), typeof(HInput),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 最大长度
        /// </summary>
        public static readonly DependencyProperty MaxLengthProperty =
            DependencyProperty.Register(nameof(MaxLength), typeof(int), typeof(HInput),
                new PropertyMetadata(0));

        /// <summary>
        /// 是否显示字数统计
        /// </summary>
        public static readonly DependencyProperty ShowWordLimitProperty =
            DependencyProperty.Register(nameof(ShowWordLimit), typeof(bool), typeof(HInput),
                new PropertyMetadata(false));

        /// <summary>
        /// 输入框类型
        /// </summary>
        public static readonly DependencyProperty InputTypeProperty =
            DependencyProperty.Register(nameof(InputType), typeof(HInputType), typeof(HInput),
                new PropertyMetadata(HInputType.Text));

        /// <summary>
        /// 标签文本
        /// </summary>
        public static readonly DependencyProperty LabelProperty =
            DependencyProperty.Register(nameof(Label), typeof(string), typeof(HInput),
                new PropertyMetadata(string.Empty));

        /// <summary>
        /// 标签宽度
        /// </summary>
        public static readonly DependencyProperty LabelWidthProperty =
            DependencyProperty.Register(nameof(LabelWidth), typeof(double), typeof(HInput),
                new PropertyMetadata(80.0));

        /// <summary>
        /// 标签位置
        /// </summary>
        public static readonly DependencyProperty LabelPositionProperty =
            DependencyProperty.Register(nameof(LabelPosition), typeof(HLabelPosition), typeof(HInput),
                new PropertyMetadata(HLabelPosition.Left));

        /// <summary>
        /// 是否显示冒号
        /// </summary>
        public static readonly DependencyProperty ShowColonProperty =
            DependencyProperty.Register(nameof(ShowColon), typeof(bool), typeof(HInput),
                new PropertyMetadata(true));

        #endregion

        #region 属性

        /// <summary>
        /// 输入框的值
        /// </summary>
        public string Text
        {
            get => (string)GetValue(TextProperty);
            set => SetValue(TextProperty, value);
        }

        /// <summary>
        /// 占位符文本
        /// </summary>
        public string Placeholder
        {
            get => (string)GetValue(PlaceholderProperty);
            set => SetValue(PlaceholderProperty, value);
        }

        /// <summary>
        /// 是否禁用
        /// </summary>
        public bool IsDisabled
        {
            get => (bool)GetValue(IsDisabledProperty);
            set => SetValue(IsDisabledProperty, value);
        }

        /// <summary>
        /// 是否只读
        /// </summary>
        public bool IsReadOnly
        {
            get => (bool)GetValue(IsReadOnlyProperty);
            set => SetValue(IsReadOnlyProperty, value);
        }

        /// <summary>
        /// 控件尺寸
        /// </summary>
        public HInputSize Size
        {
            get => (HInputSize)GetValue(SizeProperty);
            set => SetValue(SizeProperty, value);
        }

        /// <summary>
        /// 是否可清除
        /// </summary>
        public bool IsClearable
        {
            get => (bool)GetValue(IsClearableProperty);
            set => SetValue(IsClearableProperty, value);
        }

        /// <summary>
        /// 是否显示密码切换按钮
        /// </summary>
        public bool ShowPassword
        {
            get => (bool)GetValue(ShowPasswordProperty);
            set => SetValue(ShowPasswordProperty, value);
        }

        /// <summary>
        /// 前缀图标
        /// </summary>
        public string PrefixIcon
        {
            get => (string)GetValue(PrefixIconProperty);
            set => SetValue(PrefixIconProperty, value);
        }

        /// <summary>
        /// 后缀图标
        /// </summary>
        public string SuffixIcon
        {
            get => (string)GetValue(SuffixIconProperty);
            set => SetValue(SuffixIconProperty, value);
        }

        /// <summary>
        /// 最大长度
        /// </summary>
        public int MaxLength
        {
            get => (int)GetValue(MaxLengthProperty);
            set => SetValue(MaxLengthProperty, value);
        }

        /// <summary>
        /// 是否显示字数统计
        /// </summary>
        public bool ShowWordLimit
        {
            get => (bool)GetValue(ShowWordLimitProperty);
            set => SetValue(ShowWordLimitProperty, value);
        }

        /// <summary>
        /// 输入框类型
        /// </summary>
        public HInputType InputType
        {
            get => (HInputType)GetValue(InputTypeProperty);
            set => SetValue(InputTypeProperty, value);
        }

        /// <summary>
        /// 标签文本
        /// </summary>
        public string Label
        {
            get => (string)GetValue(LabelProperty);
            set => SetValue(LabelProperty, value);
        }

        /// <summary>
        /// 标签宽度
        /// </summary>
        public double LabelWidth
        {
            get => (double)GetValue(LabelWidthProperty);
            set => SetValue(LabelWidthProperty, value);
        }

        /// <summary>
        /// 标签位置
        /// </summary>
        public HLabelPosition LabelPosition
        {
            get => (HLabelPosition)GetValue(LabelPositionProperty);
            set => SetValue(LabelPositionProperty, value);
        }

        /// <summary>
        /// 是否显示冒号
        /// </summary>
        public bool ShowColon
        {
            get => (bool)GetValue(ShowColonProperty);
            set => SetValue(ShowColonProperty, value);
        }

        #endregion

        #region 路由事件

        /// <summary>
        /// 文本改变事件
        /// </summary>
        public static readonly RoutedEvent TextChangedEvent =
            EventManager.RegisterRoutedEvent(nameof(TextChanged), RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(HInput));

        /// <summary>
        /// 清除事件
        /// </summary>
        public static readonly RoutedEvent ClearEvent =
            EventManager.RegisterRoutedEvent(nameof(Clear), RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(HInput));

        /// <summary>
        /// Enter键按下事件
        /// </summary>
        public static readonly RoutedEvent EnterKeyDownEvent =
            EventManager.RegisterRoutedEvent(nameof(EnterKeyDown), RoutingStrategy.Bubble, typeof(RoutedEventHandler), typeof(HInput));

        /// <summary>
        /// 文本改变事件
        /// </summary>
        public event RoutedEventHandler TextChanged
        {
            add => AddHandler(TextChangedEvent, value);
            remove => RemoveHandler(TextChangedEvent, value);
        }

        /// <summary>
        /// 清除事件
        /// </summary>
        public event RoutedEventHandler Clear
        {
            add => AddHandler(ClearEvent, value);
            remove => RemoveHandler(ClearEvent, value);
        }

        /// <summary>
        /// Enter键按下事件
        /// </summary>
        public event RoutedEventHandler EnterKeyDown
        {
            add => AddHandler(EnterKeyDownEvent, value);
            remove => RemoveHandler(EnterKeyDownEvent, value);
        }

        #endregion

        #region 私有字段

        private TextBox? _textBox = null;
        private PasswordBox? _passwordBox = null;
        private Button? _clearButton = null;
        private Button? _passwordToggleButton = null;
        private TextBlock? _wordLimitBlock = null;
        private bool _isPasswordVisible = false;

        #endregion

        #region 构造函数

        static HInput()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(HInput), new FrameworkPropertyMetadata(typeof(HInput)));
        }

        public HInput()
        {
            // 设置默认值
            Text = string.Empty;
            Placeholder = string.Empty;
            IsDisabled = false;
            IsReadOnly = false;
            Size = HInputSize.Default;
            IsClearable = false;
            ShowPassword = false;
            PrefixIcon = string.Empty;
            SuffixIcon = string.Empty;
            MaxLength = 0;
            ShowWordLimit = false;
            InputType = HInputType.Text;
            Label = string.Empty;
            LabelWidth = 80.0;
            LabelPosition = HLabelPosition.Left;
            ShowColon = true;

            // 设置控件默认属性
            IsTabStop = true;
            Focusable = true;

            Loaded += OnLoaded;
        }

        #endregion

        #region 重写方法

        public override void OnApplyTemplate()
        {
            base.OnApplyTemplate();

            // 解绑旧事件
            UnbindEvents();

            // 获取模板中的控件
            _textBox = GetTemplateChild("PART_TextBox") as TextBox;
            _passwordBox = GetTemplateChild("PART_PasswordBox") as PasswordBox;
            _clearButton = GetTemplateChild("PART_ClearButton") as Button;
            _passwordToggleButton = GetTemplateChild("PART_PasswordToggleButton") as Button;
            _wordLimitBlock = GetTemplateChild("PART_WordLimit") as TextBlock;

            // 绑定事件
            BindEvents();

            UpdateVisualState();
            UpdateWordLimit();
        }

        private void BindEvents()
        {
            if (_textBox != null)
            {
                _textBox.TextChanged += OnTextBoxTextChanged;
                _textBox.GotFocus += OnTextBoxGotFocus;
                _textBox.LostFocus += OnTextBoxLostFocus;
                _textBox.KeyDown += OnTextBoxKeyDown;
            }

            if (_passwordBox != null)
            {
                _passwordBox.PasswordChanged += OnPasswordBoxPasswordChanged;
                _passwordBox.GotFocus += OnPasswordBoxGotFocus;
                _passwordBox.LostFocus += OnPasswordBoxLostFocus;
                _passwordBox.KeyDown += OnPasswordBoxKeyDown;
            }

            if (_clearButton != null)
            {
                _clearButton.Click += OnClearButtonClick;
            }

            if (_passwordToggleButton != null)
            {
                _passwordToggleButton.Click += OnPasswordToggleButtonClick;
            }
        }

        private void UnbindEvents()
        {
            if (_textBox != null)
            {
                _textBox.TextChanged -= OnTextBoxTextChanged;
                _textBox.GotFocus -= OnTextBoxGotFocus;
                _textBox.LostFocus -= OnTextBoxLostFocus;
                _textBox.KeyDown -= OnTextBoxKeyDown;
            }

            if (_passwordBox != null)
            {
                _passwordBox.PasswordChanged -= OnPasswordBoxPasswordChanged;
                _passwordBox.GotFocus -= OnPasswordBoxGotFocus;
                _passwordBox.LostFocus -= OnPasswordBoxLostFocus;
                _passwordBox.KeyDown -= OnPasswordBoxKeyDown;
            }

            if (_clearButton != null)
            {
                _clearButton.Click -= OnClearButtonClick;
            }

            if (_passwordToggleButton != null)
            {
                _passwordToggleButton.Click -= OnPasswordToggleButtonClick;
            }
        }

        #endregion

        #region 私有方法

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            UpdateVisualState();
        }

        private static void OnTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HInput input)
            {
                input.OnTextChanged();
            }
        }

        private static void OnIsDisabledChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HInput input)
            {
                input.UpdateVisualState();
            }
        }

        private static void OnSizeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HInput input)
            {
                input.UpdateVisualState();
            }
        }

        private void OnTextChanged()
        {
            if (_textBox != null && _textBox.Text != Text)
            {
                _textBox.Text = Text;
            }

            RaiseEvent(new RoutedEventArgs(TextChangedEvent));
            UpdateVisualState();
        }

        private void OnTextBoxTextChanged(object sender, TextChangedEventArgs e)
        {
            if (_textBox != null)
            {
                Text = _textBox.Text;
                UpdateWordLimit();
            }
        }

        private void OnTextBoxGotFocus(object sender, RoutedEventArgs e)
        {
            VisualStateManager.GoToState(this, "Focused", true);
        }

        private void OnTextBoxLostFocus(object sender, RoutedEventArgs e)
        {
            VisualStateManager.GoToState(this, "Unfocused", true);
        }

        private void OnTextBoxKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                RaiseEvent(new RoutedEventArgs(EnterKeyDownEvent));
            }
        }

        private void OnPasswordBoxPasswordChanged(object sender, RoutedEventArgs e)
        {
            if (_passwordBox != null)
            {
                Text = _passwordBox.Password;
                UpdateWordLimit();
            }
        }

        private void OnPasswordBoxGotFocus(object sender, RoutedEventArgs e)
        {
            VisualStateManager.GoToState(this, "Focused", true);
        }

        private void OnPasswordBoxLostFocus(object sender, RoutedEventArgs e)
        {
            VisualStateManager.GoToState(this, "Unfocused", true);
        }

        private void OnPasswordBoxKeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                RaiseEvent(new RoutedEventArgs(EnterKeyDownEvent));
            }
        }

        private void OnClearButtonClick(object sender, RoutedEventArgs e)
        {
            Text = string.Empty;
            RaiseEvent(new RoutedEventArgs(ClearEvent));
            _textBox?.Focus();
        }

        private void OnPasswordToggleButtonClick(object sender, RoutedEventArgs e)
        {
            _isPasswordVisible = !_isPasswordVisible;
            UpdatePasswordVisibility();
        }

        private void UpdatePasswordVisibility()
        {
            if (InputType == HInputType.Password)
            {
                if (_isPasswordVisible)
                {
                    // 显示密码：使用TextBox
                    if (_textBox != null && _passwordBox != null)
                    {
                        _textBox.Text = _passwordBox.Password;
                        _textBox.Visibility = Visibility.Visible;
                        _passwordBox.Visibility = Visibility.Collapsed;
                    }

                    // 更新图标
                    if (_passwordToggleButton?.Content is HIcon icon)
                    {
                        icon.IconName = "eye-slash";
                    }
                }
                else
                {
                    // 隐藏密码：使用PasswordBox
                    if (_textBox != null && _passwordBox != null)
                    {
                        _passwordBox.Password = _textBox.Text;
                        _textBox.Visibility = Visibility.Collapsed;
                        _passwordBox.Visibility = Visibility.Visible;
                    }

                    // 更新图标
                    if (_passwordToggleButton?.Content is HIcon icon)
                    {
                        icon.IconName = "eye";
                    }
                }
            }
        }

        private void UpdateWordLimit()
        {
            if (_wordLimitBlock != null && ShowWordLimit && MaxLength > 0)
            {
                int currentLength = string.IsNullOrEmpty(Text) ? 0 : Text.Length;
                _wordLimitBlock.Text = $"{currentLength} / {MaxLength}";
                _wordLimitBlock.Visibility = Visibility.Visible;

                // 如果超出限制，改变颜色
                _wordLimitBlock.Foreground = currentLength > MaxLength
                    ? Application.Current.Resources["DangerBrush"] as Brush ?? Brushes.Red
                    : Application.Current.Resources["TextSecondaryBrush"] as Brush ?? Brushes.Gray;
            }
            else if (_wordLimitBlock != null)
            {
                _wordLimitBlock.Visibility = Visibility.Collapsed;
            }
        }

        private void UpdateVisualState()
        {
            if (IsDisabled)
            {
                VisualStateManager.GoToState(this, "Disabled", true);
            }
            else
            {
                VisualStateManager.GoToState(this, "Normal", true);
            }

            // 更新尺寸状态
            VisualStateManager.GoToState(this, Size.ToString(), true);

            // 更新清除按钮可见性
            if (_clearButton != null)
            {
                _clearButton.Visibility = IsClearable && !string.IsNullOrEmpty(Text) && !IsDisabled ? Visibility.Visible : Visibility.Collapsed;
            }

            // 更新密码切换按钮可见性
            if (_passwordToggleButton != null)
            {
                _passwordToggleButton.Visibility = ShowPassword && InputType == HInputType.Password ? Visibility.Visible : Visibility.Collapsed;
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 聚焦到输入框
        /// </summary>
        public new void Focus()
        {
            if (InputType == HInputType.Password && _passwordBox != null)
            {
                _passwordBox.Focus();
            }
            else if (_textBox != null)
            {
                _textBox.Focus();
            }
        }

        /// <summary>
        /// 选择所有文本
        /// </summary>
        public void SelectAll()
        {
            if (InputType == HInputType.Password && _passwordBox != null)
            {
                _passwordBox.SelectAll();
            }
            else if (_textBox != null)
            {
                _textBox.SelectAll();
            }
        }

        /// <summary>
        /// 清除文本
        /// </summary>
        public void ClearText()
        {
            Text = string.Empty;
            RaiseEvent(new RoutedEventArgs(ClearEvent));
        }

        #endregion
    }

    /// <summary>
    /// 输入框尺寸枚举
    /// </summary>
    public enum HInputSize
    {
        Small = 0,
        Default = 1,
        Large = 2
    }

    /// <summary>
    /// 输入框类型枚举
    /// </summary>
    public enum HInputType
    {
        Text = 0,
        Password = 1,
        Number = 2,
        Email = 3,
        Tel = 4,
        Url = 5
    }

    /// <summary>
    /// 标签位置枚举
    /// </summary>
    public enum HLabelPosition
    {
        Left = 0,
        Top = 1,
        Right = 2
    }
}
