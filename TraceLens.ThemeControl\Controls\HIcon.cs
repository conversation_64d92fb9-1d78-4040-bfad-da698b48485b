﻿using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using TraceLens.ThemeControl.Utils;

namespace TraceLens.ThemeControl.Controls
{
    /// <summary>
    /// HIcon - 基于 FontAwesome 4.7.0 的图标控件
    /// 支持通过英文名称直接使用 FontAwesome 图标
    /// </summary>
    public class HIcon : ContentControl
    {
        #region 依赖属性

        /// <summary>
        /// 图标名称（FontAwesome 英文名，不需要 fa- 前缀）
        /// </summary>
        public static readonly DependencyProperty IconNameProperty =
            DependencyProperty.Register(nameof(IconName), typeof(string), typeof(HIcon),
                new PropertyMetadata(string.Empty, OnIconNameChanged, CoerceIconName));

        /// <summary>
        /// 图标大小
        /// </summary>
        public static readonly DependencyProperty IconSizeProperty =
            DependencyProperty.Register(nameof(IconSize), typeof(double), typeof(HIcon),
                new PropertyMetadata(16.0, OnIconSizeChanged, CoerceIconSize));

        /// <summary>
        /// 图标颜色
        /// </summary>
        public static readonly DependencyProperty IconColorProperty =
            DependencyProperty.Register(nameof(IconColor), typeof(Brush), typeof(HIcon),
                new PropertyMetadata(Brushes.Black, OnIconColorChanged, CoerceIconColor));

        #endregion

        #region 属性

        /// <summary>
        /// 图标名称（FontAwesome 英文名，不需要 fa- 前缀）
        /// 例如：home, user, settings, search 等
        /// </summary>
        public string IconName
        {
            get
            {
                var value = GetValue(IconNameProperty);
                // 如果值为 UnsetValue 或无效，返回默认值
                if (value == DependencyProperty.UnsetValue || value == null)
                {
                    return string.Empty;
                }
                return value.ToString() ?? string.Empty;
            }
            set => SetValue(IconNameProperty, value);
        }

        /// <summary>
        /// 图标大小
        /// </summary>
        public double IconSize
        {
            get
            {
                var value = GetValue(IconSizeProperty);
                // 如果值为 UnsetValue 或无效，返回默认值
                if (value == DependencyProperty.UnsetValue || !(value is double))
                {
                    return 16.0;
                }
                return (double)value;
            }
            set => SetValue(IconSizeProperty, value);
        }

        /// <summary>
        /// 图标颜色
        /// </summary>
        public Brush IconColor
        {
            get
            {
                var value = GetValue(IconColorProperty);
                // 如果值为 UnsetValue 或无效，返回默认值
                if (value == DependencyProperty.UnsetValue || !(value is Brush))
                {
                    return Brushes.Black;
                }
                return (Brush)value;
            }
            set => SetValue(IconColorProperty, value);
        }

        #endregion

        #region 构造函数

        static HIcon()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(HIcon), new FrameworkPropertyMetadata(typeof(HIcon)));
        }

        public HIcon()
        {
            // 初始化时更新图标
            UpdateIcon();
        }

        #endregion

        #region 私有方法

        private static void OnIconNameChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HIcon icon)
            {
                icon.UpdateIcon();
            }
        }

        private static void OnIconSizeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HIcon icon)
            {
                icon.UpdateIcon();
            }
        }

        private static void OnIconColorChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is HIcon icon)
            {
                icon.UpdateIcon();
            }
        }

        private static object CoerceIconName(DependencyObject d, object value)
        {
            // 确保图标名称不为 null
            if (value == null || value == DependencyProperty.UnsetValue)
            {
                return string.Empty;
            }

            var iconName = value.ToString() ?? string.Empty;
            // 移除多余的空白字符
            return iconName.Trim();
        }

        private static object CoerceIconSize(DependencyObject d, object value)
        {
            if (value is double size)
            {
                // 确保图标大小在合理范围内，最小1，最大1000
                return Math.Max(1.0, Math.Min(1000.0, size));
            }
            // 如果值无效，返回默认值
            return 16.0;
        }

        private static object CoerceIconColor(DependencyObject d, object value)
        {
            // 确保颜色不为 null
            if (value == null || value == DependencyProperty.UnsetValue)
            {
                return Brushes.Black;
            }

            // 如果不是 Brush 类型，返回默认值
            if (!(value is Brush))
            {
                return Brushes.Black;
            }

            return value;
        }

        public override void OnApplyTemplate()
        {
            base.OnApplyTemplate();
            UpdateIcon();
        }

        private void UpdateIcon()
        {
            if (string.IsNullOrEmpty(IconName))
            {
                return;
            }

            // 获取图标的 Unicode 字符
            var unicode = GetIconUnicode(IconName);
            if (!string.IsNullOrEmpty(unicode))
            {
                // 通过模板更新显示
                if (GetTemplateChild("PART_IconText") is TextBlock iconText)
                {
                    iconText.Text = unicode;
                }
            }
        }

        /// <summary>
        /// 根据图标名称获取对应的 Unicode 字符
        /// </summary>
        /// <param name="iconName">图标名称（不含 fa- 前缀）</param>
        /// <returns>Unicode 字符</returns>
        private string? GetIconUnicode(string iconName)
        {
            // 移除可能的 fa- 前缀
            if (iconName.StartsWith("fa-"))
            {
                iconName = iconName.Substring(3);
            }

            // FontAwesome 4.7.0 图标映射表（部分常用图标）
            var iconMap = GetIconMap();
            
            return iconMap.TryGetValue(iconName, out var unicode) ? unicode : null;
        }

        /// <summary>
        /// 获取 FontAwesome 4.7.0 图标映射表
        /// </summary>
        /// <returns>图标名称到 Unicode 的映射</returns>
        private Dictionary<string, string> GetIconMap()
        {
            return FontAwesome4IconMap.GetIconMap();
        }

        #endregion
    }
}
