# TraceLens HTable 控件演示

## 概述

本演示展示了基于参考DataGrid样式改进的HTable控件，具有现代化的外观和丰富的功能。

## 主要功能特性

### 1. 现代化样式
- **阴影效果**: 表格和合计栏具有专业的阴影效果
- **悬停高亮**: 鼠标悬停时行背景变为蓝色高亮 (#b8dcff)
- **选中状态**: 选中行具有明显的视觉反馈
- **圆角边框**: 现代化的圆角设计

### 2. 增强的列标题
- **排序指示器**: 使用HIcon显示排序箭头，支持动画效果
- **列调整**: 支持拖拽调整列宽
- **悬停效果**: 鼠标悬停时颜色变化
- **排序状态**: 排序时标题颜色变为主题色

### 3. 丰富的单元格功能
- **基础单元格**: 支持文本、复选框等类型
- **复制功能**: 带复制按钮的单元格样式（可选）
- **居中对齐**: 内容自动居中显示
- **高度一致**: 所有单元格高度统一

### 4. 数据绑定支持
- **ObservableCollection**: 支持数据变更通知
- **双向绑定**: 选中项支持双向绑定
- **多种数据类型**: 支持文本、日期、布尔值等

### 5. 加载状态
- **加载遮罩**: 数据加载时显示半透明遮罩
- **旋转图标**: 使用FontAwesome图标显示加载动画
- **自定义文本**: 可自定义加载提示文本

### 6. 空数据处理
- **空状态提示**: 无数据时显示友好提示
- **自定义文本**: 可自定义空数据提示文本

## 演示数据

演示应用包含以下模拟数据：
- **用户ID**: 数字类型，支持排序
- **姓名**: 文本类型，中文姓名
- **邮箱**: 文本类型，邮箱格式
- **部门**: 文本类型，部门名称
- **创建时间**: 日期时间类型，支持排序
- **状态**: 文本类型，状态标识
- **是否激活**: 布尔类型，复选框显示

## 交互功能

### 工具栏操作
1. **刷新数据**: 模拟异步加载，显示加载状态
2. **添加数据**: 在表格顶部插入新记录
3. **删除选中**: 删除当前选中的记录

### 表格操作
1. **排序**: 点击列标题进行排序
2. **选择**: 点击行选择记录
3. **滚动**: 支持水平和垂直滚动

## 技术实现

### 样式改进
- 基于参考DataGrid样式进行现代化改造
- 使用WPF的ControlTemplate和Style系统
- 支持主题色彩系统

### 数据架构
- 使用MVVM模式
- Prism框架支持
- ObservableCollection数据绑定

### 控件结构
```
HTable
├── 工具栏区域
├── 表格标题区域
├── 数据表格区域
│   ├── 列标题
│   ├── 数据行
│   └── 滚动条
├── 合计栏区域（可选）
└── 状态栏区域
```

## 使用方法

### 基本用法
```xml
<HControl:HTable ItemsSource="{Binding SampleData}"
                 Columns="{Binding TableColumns}"
                 SelectedItem="{Binding SelectedItem, Mode=TwoWay}"
                 IsLoading="{Binding IsLoading}"
                 ShowBorder="True"
                 ShowStripe="True"
                 TableSize="Default"/>
```

### 列定义
```csharp
new HTableColumn
{
    Header = "列标题",
    Property = "PropertyName",
    Width = new DataGridLength(120),
    ColumnType = HTableColumnType.Text,
    Sortable = true
}
```

## 扩展功能

### 支持的列类型
- `Text`: 文本列
- `CheckBox`: 复选框列
- `Template`: 自定义模板列

### 支持的表格尺寸
- `Small`: 小尺寸 (行高32px)
- `Default`: 默认尺寸 (行高48px)
- `Large`: 大尺寸 (行高56px)

### 边框样式
- `None`: 无边框
- `Horizontal`: 仅水平边框
- `Vertical`: 仅垂直边框
- `All`: 全边框

## 性能优化

1. **虚拟化**: 支持大数据集的虚拟化显示
2. **延迟加载**: 支持异步数据加载
3. **内存管理**: 自动清理不需要的资源

## 浏览器兼容性

本控件基于WPF技术，运行在Windows平台上，支持：
- .NET 8.0+
- Windows 10/11
- 现代化的WPF应用程序

## 总结

HTable控件成功实现了现代化的表格展示功能，具有：
- 美观的视觉效果
- 丰富的交互功能
- 良好的性能表现
- 易于使用的API
- 完整的数据绑定支持

演示应用展示了控件的各项功能，可以作为实际项目中使用HTable控件的参考。
