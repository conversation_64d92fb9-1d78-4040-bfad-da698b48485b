<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:TraceLens.ThemeControl.Controls"
                    xmlns:hicon="clr-namespace:TraceLens.ThemeControl.Controls"
                    xmlns:converters="clr-namespace:TraceLens.ThemeControl.Converters">

    <!-- DataGrid 行样式 -->
    <Style x:Key="HTableRowStyle" TargetType="DataGridRow">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridRow">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <DataGridCellsPresenter/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource FillLightBrush}"/>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Background" Value="{DynamicResource PrimaryBrush}"/>
                            <Setter Property="Foreground" Value="White"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- DataGrid 单元格样式 -->
    <Style x:Key="HTableCellStyle" TargetType="DataGridCell">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderLighterBrush}"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridCell">
                    <Border x:Name="CellBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Padding="{TemplateBinding Padding}"
                            SnapsToDevicePixels="True">
                        <ContentPresenter VerticalAlignment="{TemplateBinding VerticalAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <!-- 选中状态 -->
                        <DataTrigger Binding="{Binding IsSelected, RelativeSource={RelativeSource AncestorType=DataGridRow}}" Value="True">
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="Foreground" Value="White"/>
                        </DataTrigger>
                        <!-- 悬停状态 -->
                        <DataTrigger Binding="{Binding IsMouseOver, RelativeSource={RelativeSource AncestorType=DataGridRow}}" Value="True">
                            <Setter Property="Background" Value="Transparent"/>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- DataGrid 表头样式 -->
    <Style x:Key="HTableHeaderStyle" TargetType="DataGridColumnHeader">
        <Setter Property="Background" Value="{DynamicResource FillLightBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderLighterBrush}"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="Foreground" Value="{DynamicResource TextRegularBrush}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSizeBase}"/>
        <Setter Property="FontFamily" Value="{DynamicResource IndustrialFont}"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridColumnHeader">
                    <Border x:Name="HeaderBorder"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True">
                        <Grid>
                            <ContentPresenter Margin="{TemplateBinding Padding}"
                                            HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                            <!-- 排序指示器 -->
                            <Path x:Name="SortArrow"
                                  HorizontalAlignment="Right"
                                  VerticalAlignment="Center"
                                  Margin="0,0,8,0"
                                  Width="8"
                                  Height="6"
                                  Fill="{DynamicResource TextSecondaryBrush}"
                                  Visibility="Collapsed"/>
                            <!-- 列分隔线 -->
                            <Rectangle x:Name="VerticalSeparator"
                                      Width="1"
                                      HorizontalAlignment="Right"
                                      Fill="{DynamicResource BorderLighterBrush}"
                                      Visibility="Collapsed"/>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="{DynamicResource FillBaseBrush}"/>
                        </Trigger>
                        <Trigger Property="SortDirection" Value="Ascending">
                            <Setter TargetName="SortArrow" Property="Visibility" Value="Visible"/>
                            <Setter TargetName="SortArrow" Property="Data" Value="M 0 6 L 4 0 L 8 6 Z"/>
                        </Trigger>
                        <Trigger Property="SortDirection" Value="Descending">
                            <Setter TargetName="SortArrow" Property="Visibility" Value="Visible"/>
                            <Setter TargetName="SortArrow" Property="Data" Value="M 0 0 L 4 6 L 8 0 Z"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- HTable 控件样式 -->
    <Style TargetType="{x:Type local:HTable}">
        <Setter Property="Background" Value="{DynamicResource FillExtraLightBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBaseBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextRegularBrush}"/>
        <Setter Property="BorderThickness" Value="{DynamicResource BorderWidthBase}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSizeBase}"/>
        <Setter Property="FontFamily" Value="{DynamicResource IndustrialFont}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:HTable}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 标签 -->
                        <TextBlock x:Name="PART_Label"
                                  Grid.Column="0"
                                  Grid.Row="1"
                                  Text="{TemplateBinding Label}"
                                  FontSize="{TemplateBinding FontSize}"
                                  FontFamily="{TemplateBinding FontFamily}"
                                  Foreground="{DynamicResource TextRegularBrush}"
                                  VerticalAlignment="Top"
                                  HorizontalAlignment="Right"
                                  Margin="0,0,8,0"
                                  Width="{TemplateBinding LabelWidth}"
                                  Visibility="Collapsed"/>

                        <!-- 表格容器 -->
                        <Border Grid.Column="1"
                                Grid.Row="1"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{DynamicResource BorderRadiusBase}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <!-- 数据表格 -->
                                <ScrollViewer x:Name="DG_ScrollViewer"
                                             Grid.Row="0"
                                             Focusable="false"
                                             CanContentScroll="True"
                                             VerticalScrollBarVisibility="Auto"
                                             HorizontalScrollBarVisibility="Auto">
                                    <DataGrid x:Name="PART_DataGrid"
                                             Background="Transparent"
                                             BorderThickness="0"
                                             GridLinesVisibility="All"
                                             HorizontalGridLinesBrush="{DynamicResource BorderLighterBrush}"
                                             VerticalGridLinesBrush="{DynamicResource BorderLighterBrush}"
                                             HeadersVisibility="Column"
                                             AutoGenerateColumns="False"
                                             CanUserAddRows="False"
                                             CanUserDeleteRows="False"
                                             RowHeaderWidth="0"
                                             SelectionMode="Single"
                                             SelectionUnit="FullRow"
                                             RowStyle="{StaticResource HTableRowStyle}"
                                             CellStyle="{StaticResource HTableCellStyle}"
                                             ColumnHeaderStyle="{StaticResource HTableHeaderStyle}"
                                             ScrollViewer.CanContentScroll="False"
                                             ScrollViewer.VerticalScrollBarVisibility="Disabled"
                                             ScrollViewer.HorizontalScrollBarVisibility="Disabled"/>
                                </ScrollViewer>

                                <!-- 合计栏 -->
                                <Border x:Name="PART_SummaryBorder"
                                       Grid.Row="1"
                                       BorderThickness="0,1,0,0"
                                       BorderBrush="{DynamicResource BorderLighterBrush}"
                                       Background="{DynamicResource FillLightBrush}"
                                       Height="{TemplateBinding SummaryHeight}"
                                       Visibility="Collapsed">
                                    <ScrollViewer x:Name="PART_SummaryScrollViewer"
                                                 HorizontalScrollBarVisibility="Hidden"
                                                 VerticalScrollBarVisibility="Hidden">
                                        <ItemsControl x:Name="PART_SummaryItemsControl">
                                            <ItemsControl.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center"/>
                                                </ItemsPanelTemplate>
                                            </ItemsControl.ItemsPanel>
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Border Width="{Binding Width}"
                                                           Height="{Binding RelativeSource={RelativeSource AncestorType=local:HTable}, Path=SummaryHeight}"
                                                           BorderThickness="0,0,1,0"
                                                           BorderBrush="{DynamicResource BorderLighterBrush}">
                                                        <TextBlock Text="{Binding Content}"
                                                                  VerticalAlignment="Center"
                                                                  HorizontalAlignment="Center"
                                                                  FontWeight="Medium"
                                                                  FontSize="{Binding RelativeSource={RelativeSource AncestorType=local:HTable}, Path=FontSize}"
                                                                  FontFamily="{Binding RelativeSource={RelativeSource AncestorType=local:HTable}, Path=FontFamily}"
                                                                  Foreground="{DynamicResource TextRegularBrush}"
                                                                  Margin="12,0"/>
                                                    </Border>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </ScrollViewer>
                                </Border>

                                <!-- 空数据提示 -->
                                <TextBlock x:Name="PART_EmptyText"
                                          Grid.Row="0"
                                          Text="{TemplateBinding EmptyText}"
                                          FontSize="{TemplateBinding FontSize}"
                                          FontFamily="{TemplateBinding FontFamily}"
                                          Foreground="{DynamicResource TextSecondaryBrush}"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Margin="20"
                                          Visibility="Collapsed"/>

                                <!-- 加载遮罩 -->
                                <Grid x:Name="PART_LoadingOverlay"
                                      Grid.RowSpan="2"
                                      Background="#80FFFFFF"
                                      Visibility="Collapsed">
                                    <StackPanel HorizontalAlignment="Center"
                                               VerticalAlignment="Center"
                                               Orientation="Horizontal">
                                        <hicon:HIcon IconName="spinner"
                                                    IconSize="{DynamicResource IconSizeSmall}"
                                                    IconColor="{DynamicResource PrimaryBrush}"
                                                    Margin="0,0,8,0">
                                            <hicon:HIcon.RenderTransform>
                                                <RotateTransform/>
                                            </hicon:HIcon.RenderTransform>
                                            <hicon:HIcon.Triggers>
                                                <EventTrigger RoutedEvent="Loaded">
                                                    <BeginStoryboard>
                                                        <Storyboard RepeatBehavior="Forever">
                                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Angle"
                                                                           From="0" To="360" Duration="0:0:1"/>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </EventTrigger>
                                            </hicon:HIcon.Triggers>
                                        </hicon:HIcon>
                                        <TextBlock x:Name="PART_LoadingText"
                                                  Text="{TemplateBinding LoadingText}"
                                                  FontSize="{TemplateBinding FontSize}"
                                                  FontFamily="{TemplateBinding FontFamily}"
                                                  Foreground="{DynamicResource TextRegularBrush}"
                                                  VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </Border>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!-- 标签显示 -->
                        <DataTrigger Binding="{Binding Label, RelativeSource={RelativeSource Self}, Converter={x:Static converters:StringToVisibilityConverter.Instance}}" Value="Visible">
                            <Setter TargetName="PART_Label" Property="Visibility" Value="Visible"/>
                        </DataTrigger>

                        <!-- 显示冒号 -->
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Label, RelativeSource={RelativeSource Self}, Converter={x:Static converters:StringToVisibilityConverter.Instance}}" Value="Visible"/>
                                <Condition Binding="{Binding ShowColon, RelativeSource={RelativeSource Self}}" Value="True"/>
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Label" Property="Text" Value="{Binding Label, RelativeSource={RelativeSource TemplatedParent}, StringFormat='{}{0}:'}"/>
                        </MultiDataTrigger>

                        <!-- 外边框控制 -->
                        <Trigger Property="ShowOuterBorder" Value="False">
                            <Setter Property="BorderThickness" Value="0"/>
                        </Trigger>

                        <!-- 边框样式控制 -->
                        <Trigger Property="BorderStyle" Value="None">
                            <Setter Property="BorderThickness" Value="0"/>
                        </Trigger>

                        <!-- 合计栏显示控制 -->
                        <Trigger Property="ShowSummary" Value="True">
                            <Setter TargetName="PART_SummaryBorder" Property="Visibility" Value="Visible"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

        <!-- 尺寸样式 -->
        <Style.Triggers>
            <!-- 小尺寸 -->
            <Trigger Property="TableSize" Value="Small">
                <Setter Property="FontSize" Value="{DynamicResource FontSizeExtraSmall}"/>
                <Setter Property="RowHeight" Value="32"/>
                <Setter Property="HeaderHeight" Value="32"/>
            </Trigger>

            <!-- 大尺寸 -->
            <Trigger Property="TableSize" Value="Large">
                <Setter Property="FontSize" Value="{DynamicResource FontSizeMedium}"/>
                <Setter Property="RowHeight" Value="56"/>
                <Setter Property="HeaderHeight" Value="56"/>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
