<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:TraceLens.ThemeControl.Controls"
                    xmlns:hicon="clr-namespace:TraceLens.ThemeControl.Controls"
                    xmlns:converters="clr-namespace:TraceLens.ThemeControl.Converters">

    <!-- 阴影效果 -->
    <DropShadowEffect x:Key="HTableShadowEffect"
                      BlurRadius="8"
                      ShadowDepth="1.5"
                      Direction="270"
                      Color="#88000000"
                      Opacity=".2"
                      RenderingBias="Performance" />

    <!-- DataGrid 行样式 -->
    <Style x:Key="HTableRowStyle" TargetType="DataGridRow">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="Foreground" Value="{DynamicResource TextRegularBrush}"/>
        <Setter Property="Validation.ErrorTemplate" Value="{x:Null}"/>
        <Setter Property="ValidationErrorTemplate">
            <Setter.Value>
                <ControlTemplate>
                    <TextBlock Margin="2,0,0,0"
                              VerticalAlignment="Center"
                              Foreground="{DynamicResource DangerBrush}"
                              Text="!" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridRow">
                    <Border x:Name="DGR_Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True">
                        <SelectiveScrollingGrid>
                            <SelectiveScrollingGrid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </SelectiveScrollingGrid.ColumnDefinitions>
                            <SelectiveScrollingGrid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </SelectiveScrollingGrid.RowDefinitions>
                            <DataGridCellsPresenter Grid.Column="1"
                                                   ItemsPanel="{TemplateBinding ItemsPanel}"
                                                   SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                            <DataGridDetailsPresenter Grid.Row="1"
                                                     Grid.Column="1"
                                                     SelectiveScrollingGrid.SelectiveScrollingOrientation="{Binding AreRowDetailsFrozen, ConverterParameter={x:Static SelectiveScrollingOrientation.Vertical}, Converter={x:Static DataGrid.RowDetailsScrollingConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}"
                                                     Visibility="{TemplateBinding DetailsVisibility}" />
                            <DataGridRowHeader Grid.Row="0"
                                              Grid.RowSpan="2"
                                              Grid.Column="0"
                                              SelectiveScrollingGrid.SelectiveScrollingOrientation="Vertical"
                                              Visibility="{Binding HeadersVisibility, ConverterParameter={x:Static DataGridHeadersVisibility.Row}, Converter={x:Static DataGrid.HeadersVisibilityConverter}, RelativeSource={RelativeSource AncestorType={x:Type DataGrid}}}" />
                        </SelectiveScrollingGrid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#b8dcff"/>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Background" Value="#b8dcff"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- DataGrid 单元格样式 -->
    <Style x:Key="HTableCellStyle" TargetType="DataGridCell">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="White"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="FontWeight" Value="Regular"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Validation.ErrorTemplate" Value="{x:Null}"/>
        <Setter Property="Foreground" Value="{Binding Foreground, RelativeSource={RelativeSource AncestorType=DataGridRow}}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridCell">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True"
                            Height="48">
                        <ContentPresenter Margin="{TemplateBinding Padding}"
                                         VerticalAlignment="Center"
                                         HorizontalAlignment="Center"
                                         SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value=".56" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 带复制功能的单元格样式 -->
    <Style x:Key="HTableCellCopyStyle" TargetType="DataGridCell">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="White"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="FontWeight" Value="Regular"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Validation.ErrorTemplate" Value="{x:Null}"/>
        <Setter Property="Foreground" Value="{Binding Foreground, RelativeSource={RelativeSource AncestorType=DataGridRow}}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridCell">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            SnapsToDevicePixels="True"
                            Height="48">
                        <Border Margin="2"
                               BorderBrush="Transparent"
                               BorderThickness="1"
                               x:Name="bd2">
                            <Grid>
                                <ContentPresenter Margin="{TemplateBinding Padding}"
                                                 VerticalAlignment="Center"
                                                 HorizontalAlignment="Center"
                                                 SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                                <Button HorizontalAlignment="Right"
                                       Grid.Column="1"
                                       FocusVisualStyle="{x:Null}"
                                       Background="#b8dcff"
                                       x:Name="copyBtn"
                                       Margin="0"
                                       Padding="0"
                                       Visibility="Collapsed">
                                    <Button.Template>
                                        <ControlTemplate TargetType="Button">
                                            <Grid Background="{TemplateBinding Background}">
                                                <hicon:HIcon IconName="copy"
                                                           IconSize="14"
                                                           IconColor="Gray"
                                                           VerticalAlignment="Center"
                                                           HorizontalAlignment="Center"
                                                           Margin="3,2"/>
                                            </Grid>
                                        </ControlTemplate>
                                    </Button.Template>
                                </Button>
                            </Grid>
                        </Border>
                    </Border>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsKeyboardFocusWithin" Value="True"/>
                                <Condition Property="IsSelected" Value="True"/>
                            </MultiTrigger.Conditions>
                            <MultiTrigger.Setters>
                                <Setter Property="BorderBrush" TargetName="bd2" Value="Gray" />
                                <Setter Property="BorderThickness" TargetName="bd2" Value="1" />
                                <Setter Property="Visibility" Value="Visible" TargetName="copyBtn"/>
                            </MultiTrigger.Setters>
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value=".56" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- DataGrid 表头样式 -->
    <Style x:Key="HTableHeaderStyle" TargetType="DataGridColumnHeader">
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="TextBlock.FontWeight" Value="Medium"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Foreground" Value="Black"/>
        <Setter Property="BorderBrush" Value="White"/>
        <Setter Property="BorderThickness" Value="0,0,0,1"/>
        <Setter Property="Background" Value="{DynamicResource FillLightBrush}"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridColumnHeader">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="GoToVisible">
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="SortDirectionIndicator" Storyboard.TargetProperty="Width">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.0" Value="0" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="12" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="GoToHidden">
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="SortDirectionIndicator" Storyboard.TargetProperty="Width">
                                <EasingDoubleKeyFrame KeyTime="0:0:0.0" Value="12" />
                                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid Background="Transparent">
                        <Border Height="48"
                               Padding="{TemplateBinding Padding}"
                               Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}">
                            <Grid HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}">
                                <hicon:HIcon x:Name="SortDirectionIndicator"
                                           Width="0"
                                           IconSize="12"
                                           Margin="-16,0,0,0"
                                           HorizontalAlignment="Left"
                                           IsTabStop="False"
                                           IconColor="{DynamicResource TextSecondaryBrush}"
                                           Opacity="0.45"
                                           Visibility="{Binding CanUserSortColumns, RelativeSource={RelativeSource AncestorType=DataGrid}, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                <ContentPresenter VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                 RecognizesAccessKey="True"
                                                 HorizontalAlignment="Center"
                                                 SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                                    <ContentPresenter.Resources>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="TextTrimming" Value="WordEllipsis" />
                                            <Setter Property="FontWeight" Value="Medium"/>
                                            <Setter Property="Foreground" Value="Black"/>
                                        </Style>
                                    </ContentPresenter.Resources>
                                </ContentPresenter>
                            </Grid>
                        </Border>
                        <!-- 列调整手柄 -->
                        <Thumb x:Name="PART_LeftHeaderGripper"
                              HorizontalAlignment="Left"
                              Width="8"
                              Cursor="SizeWE"
                              Background="Transparent"
                              Padding="0" />
                        <Thumb x:Name="PART_RightHeaderGripper"
                              HorizontalAlignment="Right"
                              Width="8"
                              Cursor="SizeWE"
                              Background="Transparent"
                              Padding="0" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Foreground" Value="Gray" />
                        </Trigger>
                        <Trigger Property="SortDirection" Value="Ascending">
                            <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}" />
                            <Setter TargetName="SortDirectionIndicator" Property="IconName" Value="sort-up"/>
                        </Trigger>
                        <Trigger Property="SortDirection" Value="Descending">
                            <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}" />
                            <Setter TargetName="SortDirectionIndicator" Property="IconName" Value="sort-down"/>
                        </Trigger>
                        <DataTrigger Binding="{Binding SortDirection, RelativeSource={RelativeSource Self}, Converter={x:Static converters:StringToVisibilityConverter.Instance}}" Value="Visible">
                            <DataTrigger.EnterActions>
                                <BeginStoryboard Name="GoToVisible" Storyboard="{StaticResource GoToVisible}" />
                            </DataTrigger.EnterActions>
                            <DataTrigger.ExitActions>
                                <RemoveStoryboard BeginStoryboardName="GoToVisible" />
                                <BeginStoryboard Storyboard="{StaticResource GoToHidden}" />
                            </DataTrigger.ExitActions>
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- HTable 控件样式 -->
    <Style TargetType="{x:Type local:HTable}">
        <Setter Property="Background" Value="{DynamicResource FillExtraLightBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBaseBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource TextRegularBrush}"/>
        <Setter Property="BorderThickness" Value="{DynamicResource BorderWidthBase}"/>
        <Setter Property="FontSize" Value="{DynamicResource FontSizeBase}"/>
        <Setter Property="FontFamily" Value="{DynamicResource IndustrialFont}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type local:HTable}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 标签 -->
                        <TextBlock x:Name="PART_Label"
                                  Grid.Column="0"
                                  Grid.Row="1"
                                  Text="{TemplateBinding Label}"
                                  FontSize="{TemplateBinding FontSize}"
                                  FontFamily="{TemplateBinding FontFamily}"
                                  Foreground="{DynamicResource TextRegularBrush}"
                                  VerticalAlignment="Top"
                                  HorizontalAlignment="Right"
                                  Margin="0,0,8,0"
                                  Width="{TemplateBinding LabelWidth}"
                                  Visibility="Collapsed"/>

                        <!-- 表格容器 -->
                        <Border Grid.Column="1"
                                Grid.Row="1"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{DynamicResource BorderRadiusBase}">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <!-- 数据表格 -->
                                <ScrollViewer x:Name="DG_ScrollViewer"
                                             Grid.Row="0"
                                             Focusable="false"
                                             CanContentScroll="True"
                                             VerticalScrollBarVisibility="Auto"
                                             HorizontalScrollBarVisibility="Auto">
                                    <DataGrid x:Name="PART_DataGrid"
                                             Background="Transparent"
                                             BorderThickness="0"
                                             GridLinesVisibility="All"
                                             HorizontalGridLinesBrush="{DynamicResource BorderLighterBrush}"
                                             VerticalGridLinesBrush="{DynamicResource BorderLighterBrush}"
                                             HeadersVisibility="Column"
                                             AutoGenerateColumns="False"
                                             CanUserAddRows="False"
                                             CanUserDeleteRows="False"
                                             RowHeaderWidth="0"
                                             SelectionMode="Single"
                                             SelectionUnit="FullRow"
                                             RowStyle="{StaticResource HTableRowStyle}"
                                             CellStyle="{StaticResource HTableCellStyle}"
                                             ColumnHeaderStyle="{StaticResource HTableHeaderStyle}"
                                             ScrollViewer.CanContentScroll="False"
                                             ScrollViewer.VerticalScrollBarVisibility="Disabled"
                                             ScrollViewer.HorizontalScrollBarVisibility="Disabled"/>
                                </ScrollViewer>

                                <!-- 合计栏 -->
                                <Border x:Name="PART_SummaryBorder"
                                       Grid.Row="1"
                                       BorderThickness="0,1,0,0"
                                       BorderBrush="{DynamicResource BorderLighterBrush}"
                                       Background="{DynamicResource FillLightBrush}"
                                       Height="{TemplateBinding SummaryHeight}"
                                       Visibility="Collapsed">
                                    <ScrollViewer x:Name="PART_SummaryScrollViewer"
                                                 HorizontalScrollBarVisibility="Hidden"
                                                 VerticalScrollBarVisibility="Hidden">
                                        <ItemsControl x:Name="PART_SummaryItemsControl">
                                            <ItemsControl.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center"/>
                                                </ItemsPanelTemplate>
                                            </ItemsControl.ItemsPanel>
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Border Width="{Binding Width}"
                                                           Height="{Binding RelativeSource={RelativeSource AncestorType=local:HTable}, Path=SummaryHeight}"
                                                           BorderThickness="0,0,1,0"
                                                           BorderBrush="{DynamicResource BorderLighterBrush}">
                                                        <TextBlock Text="{Binding Content}"
                                                                  VerticalAlignment="Center"
                                                                  HorizontalAlignment="Center"
                                                                  FontWeight="Medium"
                                                                  FontSize="{Binding RelativeSource={RelativeSource AncestorType=local:HTable}, Path=FontSize}"
                                                                  FontFamily="{Binding RelativeSource={RelativeSource AncestorType=local:HTable}, Path=FontFamily}"
                                                                  Foreground="{DynamicResource TextRegularBrush}"
                                                                  Margin="12,0"/>
                                                    </Border>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </ScrollViewer>
                                </Border>

                                <!-- 空数据提示 -->
                                <TextBlock x:Name="PART_EmptyText"
                                          Grid.Row="0"
                                          Text="{TemplateBinding EmptyText}"
                                          FontSize="{TemplateBinding FontSize}"
                                          FontFamily="{TemplateBinding FontFamily}"
                                          Foreground="{DynamicResource TextSecondaryBrush}"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Margin="20"
                                          Visibility="Collapsed"/>

                                <!-- 加载遮罩 -->
                                <Grid x:Name="PART_LoadingOverlay"
                                      Grid.RowSpan="2"
                                      Background="#80FFFFFF"
                                      Visibility="Collapsed">
                                    <StackPanel HorizontalAlignment="Center"
                                               VerticalAlignment="Center"
                                               Orientation="Horizontal">
                                        <hicon:HIcon IconName="spinner"
                                                    IconSize="{DynamicResource IconSizeSmall}"
                                                    IconColor="{DynamicResource PrimaryBrush}"
                                                    Margin="0,0,8,0">
                                            <hicon:HIcon.RenderTransform>
                                                <RotateTransform/>
                                            </hicon:HIcon.RenderTransform>
                                            <hicon:HIcon.Triggers>
                                                <EventTrigger RoutedEvent="Loaded">
                                                    <BeginStoryboard>
                                                        <Storyboard RepeatBehavior="Forever">
                                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.Angle"
                                                                           From="0" To="360" Duration="0:0:1"/>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </EventTrigger>
                                            </hicon:HIcon.Triggers>
                                        </hicon:HIcon>
                                        <TextBlock x:Name="PART_LoadingText"
                                                  Text="{TemplateBinding LoadingText}"
                                                  FontSize="{TemplateBinding FontSize}"
                                                  FontFamily="{TemplateBinding FontFamily}"
                                                  Foreground="{DynamicResource TextRegularBrush}"
                                                  VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </Border>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <!-- 标签显示 -->
                        <DataTrigger Binding="{Binding Label, RelativeSource={RelativeSource Self}, Converter={x:Static converters:StringToVisibilityConverter.Instance}}" Value="Visible">
                            <Setter TargetName="PART_Label" Property="Visibility" Value="Visible"/>
                        </DataTrigger>

                        <!-- 显示冒号 -->
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding Label, RelativeSource={RelativeSource Self}, Converter={x:Static converters:StringToVisibilityConverter.Instance}}" Value="Visible"/>
                                <Condition Binding="{Binding ShowColon, RelativeSource={RelativeSource Self}}" Value="True"/>
                            </MultiDataTrigger.Conditions>
                            <Setter TargetName="PART_Label" Property="Text" Value="{Binding Label, RelativeSource={RelativeSource TemplatedParent}, StringFormat='{}{0}:'}"/>
                        </MultiDataTrigger>

                        <!-- 外边框控制 -->
                        <Trigger Property="ShowOuterBorder" Value="False">
                            <Setter Property="BorderThickness" Value="0"/>
                        </Trigger>

                        <!-- 边框样式控制 -->
                        <Trigger Property="BorderStyle" Value="None">
                            <Setter Property="BorderThickness" Value="0"/>
                        </Trigger>

                        <!-- 合计栏显示控制 -->
                        <Trigger Property="ShowSummary" Value="True">
                            <Setter TargetName="PART_SummaryBorder" Property="Visibility" Value="Visible"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

        <!-- 尺寸样式 -->
        <Style.Triggers>
            <!-- 小尺寸 -->
            <Trigger Property="TableSize" Value="Small">
                <Setter Property="FontSize" Value="{DynamicResource FontSizeExtraSmall}"/>
                <Setter Property="RowHeight" Value="32"/>
                <Setter Property="HeaderHeight" Value="32"/>
            </Trigger>

            <!-- 大尺寸 -->
            <Trigger Property="TableSize" Value="Large">
                <Setter Property="FontSize" Value="{DynamicResource FontSizeMedium}"/>
                <Setter Property="RowHeight" Value="56"/>
                <Setter Property="HeaderHeight" Value="56"/>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
